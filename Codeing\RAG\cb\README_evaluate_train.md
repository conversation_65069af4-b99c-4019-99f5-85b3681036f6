# 训练集评估脚本使用说明

## 功能介绍

`evaluate_train.py` 是一个用于评估RAG系统在训练集上表现的**多进程评估脚本**，主要功能包括：

1. 📖 **读取训练数据**：从 `data/train.json` 读取问题
2. 🚀 **多进程生成答案**：使用 `main.py` 中的多进程RAG系统并行生成答案  
3. ✅ **验证匹配**：比较生成的 `filename` 和 `page` 与训练集中的标准答案
4. 📊 **输出报告**：统计准确率并输出不匹配的样本

## 快速开始

### 运行脚本

```bash
# 直接运行，会有交互式选择界面
python evaluate_train.py
```

### 交互式选择

运行脚本后，你会看到以下选择界面：

```
============================================================
训练集评估脚本
============================================================

请选择评估模式:
1. 快速测试 (10个样本)
2. 中等测试 (50个样本)  
3. 大规模测试 (200个样本)
4. 完整评估 (全部711个样本)

请输入选择 (1-4, 默认1): 2

将评估 50 个样本
请输入进程数 (1-8, 默认4): 4

使用 4 个进程进行评估
------------------------------------------------------------
```

### 评估模式说明

| 模式 | 样本数 | 适用场景 | 预计耗时 |
|------|--------|----------|----------|
| **快速测试** | 10个 | 开发调试，快速验证 | ~1分钟 |
| **中等测试** | 50个 | 功能测试，初步评估 | ~5分钟 |
| **大规模测试** | 200个 | 性能测试，深度评估 | ~20分钟 |
| **完整评估** | 711个 | 正式评估，完整报告 | ~60分钟 |

## 输出文件

### 1. 详细评估结果 (`train_evaluation_results.json`)

包含完整的评估统计和不匹配样本详情：

```json
{
  "total_samples": 100,
  "success_count": 95,
  "filename_correct": 80,
  "page_correct": 85,
  "both_correct": 75,
  "filename_accuracy": 84.2,
  "page_accuracy": 89.5,
  "both_accuracy": 78.9,
  "success_rate": 95.0,
  "total_time": 450.2,
  "avg_time_per_sample": 4.5,
  "mismatches": [
    {
      "index": 5,
      "question": "广联达在建筑行业的数字化转型中...",
      "expected": "广联达-数字化转型报告.pdf 第3页",
      "predicted": "广联达-数字化转型报告.pdf 第5页",
      "filename_match": true,
      "page_match": false
    }
  ]
}
```

### 2. 预测结果 (`train_predictions.json`)

类似 `sample_submit.json` 格式的预测结果：

```json
[
  {
    "filename": "广联达-数字化转型报告.pdf",
    "page": 3,
    "question": "广联达在建筑行业的数字化转型中...",
    "answer": "根据文档内容，广联达在建筑行业的数字化转型中..."
  }
]
```

## 评估指标

### 准确率指标

- **文件名准确率**：预测的 `filename` 与标准答案完全匹配的比例
- **页码准确率**：预测的 `page` 与标准答案匹配的比例  
- **完全匹配准确率**：`filename` 和 `page` 都匹配的比例
- **成功处理率**：成功生成答案（无错误）的比例

### 性能指标

- **总耗时**：评估所有样本的总时间
- **平均每题耗时**：单个问题的平均处理时间

## 日志和监控

脚本会生成详细的日志文件 `evaluate_train.log`，包含：

- 处理进度信息
- 错误和异常详情
- 性能统计信息

控制台输出示例：

```
2025-01-XX XX:XX:XX - INFO - 加载了 711 个训练样本
2025-01-XX XX:XX:XX - INFO - 开始评估 100 个样本 (索引 0-99)
2025-01-XX XX:XX:XX - INFO - 评估进度: 1/100 (总体: 1/711)
2025-01-XX XX:XX:XX - INFO - 评估进度: 2/100 (总体: 2/711)
...

============================================================
评估结果摘要
============================================================
总样本数: 100
成功处理: 95 (95.0%)
文件名准确率: 84.2% (80/95)
页码准确率: 89.5% (85/95)
完全匹配准确率: 78.9% (75/95)
总耗时: 450.2秒
平均每题: 4.5秒
不匹配样本数: 20

------------------------------------------------------------
不匹配样本示例 (前10个):
------------------------------------------------------------

1. 样本索引: 5
   问题: 广联达在建筑行业的数字化转型中...
   期望: 广联达-数字化转型报告.pdf 第3页
   预测: 广联达-数字化转型报告.pdf 第5页
   文件名匹配: √
   页码匹配: X
```

## 使用建议

### 多进程性能

脚本支持1-8个进程并行处理：

- **单进程 (1)**：适合调试和资源受限环境
- **4进程 (默认)**：平衡性能和资源使用
- **8进程**：最大化性能，需要足够的API配额

### 性能对比

| 进程数 | 10个样本 | 50个样本 | 200个样本 | 全部样本(711个) |
|--------|----------|----------|-----------|----------------|
| 1进程  | ~2.5分钟 | ~12分钟  | ~48分钟   | ~3小时         |
| 4进程  | ~40秒    | ~3分钟   | ~12分钟   | ~45分钟        |
| 8进程  | ~25秒    | ~2分钟   | ~8分钟    | ~30分钟        |

*注：实际时间取决于网络条件和API响应速度*

## 前置条件

确保以下文件和目录存在：

1. ✅ **向量数据库**：`data/db/` 目录（运行 `04_build_vector_index_v2.py` 构建）
2. ✅ **训练数据**：`data/train.json` 文件
3. ✅ **环境配置**：`.env` 文件中的API密钥设置

## 故障排除

### 常见问题

1. **向量数据库不存在**
   ```
   错误: 向量数据库目录不存在
   解决: 运行 python 04_build_vector_index_v2.py
   ```

2. **API密钥未设置**
   ```
   错误: 未设置 CN_API_KEY
   解决: 在 .env 文件中设置 CN_API_KEY
   ```

3. **内存不足**
   ```
   解决: 选择较小的评估模式（快速测试或中等测试）
   ```

### 性能优化

- **选择合适的模式**：开发阶段使用快速测试，正式评估使用完整模式
- **调整进程数**：根据你的API配额和机器性能选择进程数
- **监控日志**：查看 `evaluate_train.log` 文件了解详细进度和错误信息
- **网络稳定性**：确保网络连接稳定，避免API调用失败

## 结果分析

### 准确率分析

- **完全匹配准确率 > 80%**：系统表现良好
- **页码准确率明显低于文件名准确率**：可能需要优化页码提取逻辑
- **成功处理率 < 95%**：需要检查系统稳定性

### 错误模式分析

查看 `mismatches` 列表中的不匹配样本，分析常见错误模式：

- 文件名匹配但页码不匹配
- 页码接近但不完全匹配（±1页）
- 特定类型文档的系统性错误

这些分析有助于针对性地改进RAG系统！