#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试取消去重后的数据库匹配效果
"""

import os
import logging
from pathlib import Path
from dotenv import load_dotenv

# 导入数据库匹配器
from modules.vector_retriever import DatabaseMatcher

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_no_deduplication_effect():
    """测试取消去重的效果"""
    
    print("=" * 80)
    print("取消去重后的数据库匹配测试")
    print("=" * 80)
    
    # 初始化匹配器
    db_dir = Path("data/db")
    matcher = DatabaseMatcher(db_dir)
    
    print(f"📊 统计信息:")
    print(f"   总项目数: {len(matcher.db_metadata)}")
    print(f"   参与匹配数: {len(matcher.company_representatives)}")
    print(f"   匹配覆盖率: {len(matcher.company_representatives) / len(matcher.db_metadata) * 100:.1f}%")
    
    # 关键失败案例测试
    test_cases = [
        {
            "query": "如何分析广联达在2006年至2020年间营业收入和净利润的增长趋势？",
            "expected_keyword": "三条增长曲线",
            "company": "广联达"
        },
        {
            "query": "如何分析广联达在\"八三\"规划中设立的\"3+X\"业务发展目标？",
            "expected_keyword": "三条增长曲线",
            "company": "广联达"
        },
        {
            "query": "广联达的股权激励措施对公司的人效和薪酬有何影响？",
            "expected_keyword": "三条增长曲线",
            "company": "广联达"
        },
        {
            "query": "关于伊利股份（600887）的深度报告《王者荣耀，行稳致远》，请问在2009年之后，伊利是如何通过营销策略实现了后来居上？",
            "expected_keyword": "王者荣耀",
            "company": "伊利股份"
        },
        {
            "query": "根据凌云股份（600480）的深度研究报告，请问亚大集团在乘用车和商用车管路产品应用方面分别有哪些具体的产品？",
            "expected_keyword": "凌云股份",
            "company": "凌云股份"
        }
    ]
    
    print(f"\n🔍 关键测试案例:")
    
    success_count = 0
    for i, case in enumerate(test_cases):
        print(f"\n{i+1}. 查询: {case['query'][:50]}...")
        print(f"   期望关键词: {case['expected_keyword']}")
        
        # 获取匹配结果
        matches = matcher.match_databases(case['query'], max_matches=5)
        
        # 检查结果
        found_expected = False
        for rank, (db_name, score) in enumerate(matches):
            # 获取项目名称
            project_name = "未知项目"
            for info in matcher.company_representatives.values():
                if info['db_name'] == db_name:
                    project_name = info['project_name']
                    break
            
            if case['expected_keyword'] in project_name:
                found_expected = True
                print(f"   ✅ 找到期望项目: 排名第{rank+1}位")
                print(f"   📄 项目: {project_name[:60]}...")
                print(f"   📊 分数: {score:.3f}")
                if rank == 0:
                    success_count += 1
                break
        
        if not found_expected:
            print(f"   ❌ 未找到期望项目")
            print(f"   TOP1: {matches[0][0] if matches else '无结果'}...")
        
        # 显示TOP3
        print(f"   TOP3:")
        for rank, (db_name, score) in enumerate(matches[:3]):
            project_name = "未知项目"
            for info in matcher.company_representatives.values():
                if info['db_name'] == db_name:
                    project_name = info['project_name'][:50] + "..."
                    break
            status = "✅" if case['expected_keyword'] in project_name else ""
            print(f"     {rank+1}. {project_name} (分数: {score:.3f}) {status}")
    
    # 统计结果
    print(f"\n📊 测试结果:")
    print(f"   TOP1成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    
    if success_count > len(test_cases) * 0.6:
        print(f"   🎉 取消去重效果显著！建议采用此方案")
    else:
        print(f"   ⚠️  仍需进一步优化匹配算法")

def analyze_guanglianda_projects():
    """分析广联达项目的匹配情况"""
    
    print(f"\n" + "=" * 80)
    print("广联达项目匹配分析")
    print("=" * 80)
    
    # 初始化匹配器
    db_dir = Path("data/db")
    matcher = DatabaseMatcher(db_dir)
    
    # 广联达相关查询
    guanglianda_query = "如何分析广联达在2006年至2020年间营业收入和净利润的增长趋势？"
    
    print(f"🔍 测试查询: {guanglianda_query[:50]}...")
    
    # 获取所有广联达项目
    guanglianda_projects = []
    for info in matcher.company_representatives.values():
        if "广联达" in info['company_name']:
            guanglianda_projects.append(info)
    
    print(f"\n📋 广联达项目列表 (共{len(guanglianda_projects)}个):")
    for i, proj in enumerate(guanglianda_projects):
        print(f"   {i+1}. {proj['project_name'][:80]}...")
    
    # 获取匹配结果
    matches = matcher.match_databases(guanglianda_query, max_matches=10)
    
    print(f"\n🎯 匹配结果 (TOP10):")
    guanglianda_in_top10 = 0
    for rank, (db_name, score) in enumerate(matches):
        project_name = "未知项目"
        for info in matcher.company_representatives.values():
            if info['db_name'] == db_name:
                project_name = info['project_name']
                break
        
        is_guanglianda = "广联达" in project_name
        if is_guanglianda:
            guanglianda_in_top10 += 1
        
        status = "🏢" if is_guanglianda else "  "
        print(f"   {rank+1}. {status} {project_name[:60]}... (分数: {score:.3f})")
    
    print(f"\n📊 广联达项目在TOP10中的数量: {guanglianda_in_top10}")

if __name__ == "__main__":
    test_no_deduplication_effect()
    analyze_guanglianda_projects()