#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 FAISS 的文档分块和向量索引构建脚本

功能特性：
1. 支持页码分块和标题分块两种策略
2. 使用 Qwen/Qwen3-Embedding-8B 嵌入模型
3. 为每个项目目录单独构建 FAISS 向量数据库
4. 存储丰富的元数据以支持检索和上下文扩展
5. 自动处理页码修正（+1）

作者: AI Assistant
日期: 2025年
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import re
import hashlib
import time
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
from dotenv import load_dotenv

# LangChain imports
from langchain.text_splitter import RecursiveCharacterTextSplitter, MarkdownHeaderTextSplitter
from langchain.schema import Document
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_openai import OpenAIEmbeddings

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 恢复INFO级别
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vector_index_build.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ChunkMetadata:
    """文本块元数据"""
    project_name: str
    page_number: int
    chunk_type: str  # 'page' or 'title'
    title_level: Optional[int] = None
    title_content: Optional[str] = None
    chunk_index: Optional[int] = None
    total_chunks: Optional[int] = None
    source_file: str = ""
    text_length: int = 0
    text_level: Optional[int] = None  # 文本层级（来自content_list.json）

class SequentialPageMatcher:
    """顺序页码匹配器 - 按顺序匹配MD段落和content_list.json中的文本块"""
    
    def __init__(self, metadata_list: List[Dict]):
        self.metadata_list = metadata_list
        self.current_index = 0
        self.text_blocks = [item for item in metadata_list if item.get('type') == 'text']
        self.total_blocks = len(self.text_blocks)
        self.matched_count = 0
        self.skipped_count = 0
        
        logger.info(f"🔄 初始化顺序匹配器: 共 {self.total_blocks} 个文本块待匹配")
    
    def find_next_page_match(self, chunk_content: str) -> Optional[Dict]:
        """
        为下一个段落查找匹配的页码（顺序查找）
        
        Args:
            chunk_content: 段落内容
            
        Returns:
            匹配信息字典，包含页码等信息；None表示未找到匹配
        """
        if not chunk_content.strip():
            logger.warning("⚠️ 段落内容为空，跳过匹配")
            return None
        
        # 检查是否已到达文件末尾
        if self.current_index >= self.total_blocks:
            logger.warning(f"⚠️ 已到达JSON文件末尾({self.total_blocks})，剩余MD段落将使用标题页码")
            return None
        
        chunk_content_clean = chunk_content.lower().strip()
        chunk_words = set(chunk_content_clean.split())
        
        if not chunk_words:
            logger.warning("⚠️ 段落无有效关键词，跳过匹配")
            return None
        
        # 特殊内容处理
        is_table_content = chunk_content.startswith('<table>') or '<td>' in chunk_content
        is_metadata_content = any(keyword in chunk_content_clean for keyword in 
                                ['资料来源', 'source:', 'note:', '数据来源', '注:', '备注'])
        
        original_index = self.current_index
        search_range = min(20, self.total_blocks - self.current_index)
        
        # 如果搜索范围为0，直接返回
        if search_range == 0:
            logger.warning(f"⚠️ 无搜索范围，已到达文件末尾")
            return None
        
        # 只在特定条件下显示搜索信息
        if search_range < 10:  # 搜索范围较小时显示
            logger.info(f"🔍 顺序匹配: 从位置 {self.current_index}/{self.total_blocks} 开始搜索，范围={search_range}")
        elif self.current_index % 20 == 0:  # 每20个位置显示一次进度
            logger.info(f"🔍 匹配进度: 当前位置 {self.current_index}/{self.total_blocks} ({self.current_index/self.total_blocks*100:.1f}%)")
        
        # 从当前位置开始向前搜索
        for offset in range(search_range):
            current_pos = self.current_index + offset
            if current_pos >= self.total_blocks:
                break
                
            text_block = self.text_blocks[current_pos]
            item_text = text_block.get('text', '').strip()
            
            if not item_text or len(item_text) < 3:  # 跳过过短的文本
                continue
            
            # 检查匹配
            match_result = self._check_text_match(chunk_content_clean, chunk_words, item_text, text_block, is_table_content, is_metadata_content)
            
            if match_result:
                # 找到匹配，更新位置指针
                self.current_index = current_pos + 1
                self.matched_count += 1
                
                # 重置连续失败计数器
                self._consecutive_failures = 0
                
                # 统计跳过的块数
                skipped_in_this_search = offset
                self.skipped_count += skipped_in_this_search
                
                logger.info(f"✅ 顺序匹配成功: 位置 {current_pos}, 页码 {match_result['page_number']} ({match_result['match_type']})")
                logger.info(f"📊 匹配统计: 成功={self.matched_count}, 跳过={self.skipped_count}, 当前位置={self.current_index}/{self.total_blocks}")
                if skipped_in_this_search > 0:
                    logger.info(f"⏭️ 本次搜索跳过 {skipped_in_this_search} 个文本块")
                
                return match_result
        
        # 未找到匹配，提供详细的诊断信息
        end_pos = min(original_index + search_range, self.total_blocks)
        logger.warning(f"❌ 顺序匹配失败: 在位置 {original_index}-{end_pos} 范围内未找到匹配")
        
        # 每10个匹配失败显示一次详细信息，避免日志过多
        if hasattr(self, '_debug_counter'):
            self._debug_counter += 1
        else:
            self._debug_counter = 1
            
        if self._debug_counter % 10 == 1:  # 每10次失败显示一次详情
            logger.info(f"📝 失败段落示例: {chunk_content[:150]}...")
            logger.info("🔍 搜索范围内的JSON文本块样例:")
            sample_count = 0
            for offset in range(min(3, search_range)):  # 只显示前3个作为样例
                pos = original_index + offset
                if pos < self.total_blocks:
                    sample_text = self.text_blocks[pos].get('text', '')[:50]
                    sample_page = self.text_blocks[pos].get('page_idx', -1) + 1
                    logger.info(f"  位置{pos}: 页码{sample_page} - {sample_text}...")
                    sample_count += 1
            if search_range > sample_count:
                logger.info(f"  ... 还有 {search_range - sample_count} 个文本块未显示")
            
            # 分析可能的原因
            chunk_len = len(chunk_content)
            chunk_words_count = len(chunk_words)
            if chunk_len < 10:
                logger.info("💡 可能原因: 段落内容过短，难以匹配")
            elif chunk_len > 2000:
                logger.info("💡 可能原因: 段落内容过长，可能包含多个JSON文本块")
            elif chunk_words_count < 5:
                logger.info("💡 可能原因: 有效关键词太少")
            else:
                logger.info("💡 可能原因: 1)内容经过编辑修改 2)搜索窗口不够 3)匹配阈值过高")
            
            logger.info(f"ℹ️ 注: 为避免日志过多，详细信息每10次失败显示一次")
        
        # 推进指针，避免重复搜索相同区域
        # 如果已经接近末尾且连续失败，跳跃更大的步长
        if search_range <= 5 and hasattr(self, '_consecutive_failures'):
            self._consecutive_failures += 1
            if self._consecutive_failures > 3:
                self.current_index = self.total_blocks  # 直接跳到末尾，避免无效搜索
                logger.info("🚀 连续匹配失败，跳过到文件末尾")
                return None
        else:
            if not hasattr(self, '_consecutive_failures'):
                self._consecutive_failures = 0
            self._consecutive_failures += 1
        
        self.current_index = min(self.current_index + 1, self.total_blocks)
        
        return None
    
    def _check_text_match(self, chunk_content_clean: str, chunk_words: set, item_text: str, text_block: Dict, 
                          is_table_content: bool = False, is_metadata_content: bool = False) -> Optional[Dict]:
        """检查单个文本块是否匹配"""
        item_text_clean = item_text.lower().strip()
        item_words = set(item_text_clean.split())
        
        if not item_words:
            return None
        
        # 方法1: 精确文本包含（最高优先级）
        if item_text_clean in chunk_content_clean:
            page_idx = text_block.get('page_idx', 0)
            return {
                'page_number': page_idx + 1,
                'match_type': 'exact_inclusion',
                'match_score': 1.0,
                'matched_text': item_text[:80],
                'block_index': text_block.get('index', -1)
            }
        
        # 方法2: 表格内容特殊处理
        if is_table_content:
            # 从HTML表格中提取纯文本进行匹配
            import re
            # 移除HTML标签，提取表格中的文本内容
            table_text = re.sub(r'<[^>]+>', ' ', chunk_content_clean)
            table_text = re.sub(r'\s+', ' ', table_text).strip()
            table_words = set(table_text.split())
            
            # 检查表格文本内容是否包含JSON文本
            if item_text_clean in table_text:
                page_idx = text_block.get('page_idx', 0)
                return {
                    'page_number': page_idx + 1,
                    'match_type': 'table_content_match',
                    'match_score': 0.9,
                    'matched_text': item_text[:80],
                    'block_index': text_block.get('index', -1)
                }
            
            # 表格内容降低覆盖率要求
            covered_words = item_words & table_words
            if covered_words:
                coverage_rate = len(covered_words) / len(item_words)
                if coverage_rate > 0.6:  # 表格内容降低到60%
                    page_idx = text_block.get('page_idx', 0)
                    return {
                        'page_number': page_idx + 1,
                        'match_type': 'table_coverage_match',
                        'match_score': coverage_rate,
                        'matched_text': item_text[:80],
                        'block_index': text_block.get('index', -1),
                        'coverage_details': f"{len(covered_words)}/{len(item_words)} 词汇匹配(表格)"
                    }
        
        # 方法3: 元数据内容特殊处理
        if is_metadata_content:
            # 元数据内容更宽松的匹配
            covered_words = item_words & chunk_words
            if covered_words:
                coverage_rate = len(covered_words) / len(item_words)
                if coverage_rate > 0.5:  # 元数据内容降低到50%
                    page_idx = text_block.get('page_idx', 0)
                    return {
                        'page_number': page_idx + 1,
                        'match_type': 'metadata_match',
                        'match_score': coverage_rate,
                        'matched_text': item_text[:80],
                        'block_index': text_block.get('index', -1),
                        'coverage_details': f"{len(covered_words)}/{len(item_words)} 词汇匹配(元数据)"
                    }
        
        # 方法4: 标准高覆盖率匹配（备用）
        covered_words = item_words & chunk_words
        if covered_words:
            coverage_rate = len(covered_words) / len(item_words)
            
            # 要求较高的覆盖率
            if coverage_rate > 0.8:  # 80%以上覆盖率
                page_idx = text_block.get('page_idx', 0)
                return {
                    'page_number': page_idx + 1,
                    'match_type': 'high_coverage',
                    'match_score': coverage_rate,
                    'matched_text': item_text[:80],
                    'block_index': text_block.get('index', -1),
                    'coverage_details': f"{len(covered_words)}/{len(item_words)} 词汇匹配"
                }
        
        return None
    
    def get_progress_info(self) -> Dict:
        """获取匹配进度信息"""
        progress = self.current_index / self.total_blocks if self.total_blocks > 0 else 0
        return {
            'current_position': self.current_index,
            'total_blocks': self.total_blocks,
            'progress_percent': round(progress * 100, 1),
            'matched_count': self.matched_count,
            'skipped_count': self.skipped_count,
            'remaining_blocks': self.total_blocks - self.current_index
        }

class DocumentChunker:
    """文档分块器"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n\n", "\n", "。", "！", "？", "；", "，", " ", ""]
        )
        
        # 配置 Markdown 标题分割器
        self.headers_to_split_on = [
            ("#", "Header 1"),
            ("##", "Header 2"),
            ("###", "Header 3"),
        ]
        self.markdown_splitter = MarkdownHeaderTextSplitter(
            headers_to_split_on=self.headers_to_split_on
        )
    
    def extract_header_info(self, metadata: Dict[str, Any]) -> Tuple[int, str]:
        """从 MarkdownHeaderTextSplitter 的元数据中提取标题信息"""
        title_level = 0
        title_content = ""
        
        # MarkdownHeaderTextSplitter 会将标题信息存储在 metadata 中
        for i in range(1, 4):  # Header 1 到 Header 3
            header_key = f"Header {i}"
            if header_key in metadata:
                title_level = i
                title_content = metadata[header_key]
        
        return title_level, title_content
    

    
    def chunk_by_title_with_pages(self, content: str, metadata_list: List[Dict], project_name: str) -> List[Document]:
        """按标题分块，同时从content_list.json中提取准确的页码信息（顺序匹配版本）"""
        documents = []
        
        # 首先构建标题-页码映射表
        title_page_mapping = self.build_title_page_mapping(metadata_list)
        
        # 创建顺序匹配器
        sequential_matcher = SequentialPageMatcher(metadata_list)
        
        try:
            # 使用 MarkdownHeaderTextSplitter 按标题分割
            md_header_splits = self.markdown_splitter.split_text(content)
            
            for md_doc in md_header_splits:
                if not md_doc.page_content.strip():
                    continue
                
                # 提取标题信息
                title_level, title_content = self.extract_header_info(md_doc.metadata)
                
                # 从映射表中查找对应的页码信息
                page_numbers = self.find_pages_for_title(title_content, title_page_mapping)
                
                # 如果文档块仍然很大，使用 RecursiveCharacterTextSplitter 进一步分割
                if len(md_doc.page_content) > self.chunk_size:
                    sub_chunks = self.text_splitter.split_text(md_doc.page_content)
                else:
                    sub_chunks = [md_doc.page_content]
                
                # 为每个子块创建文档
                for i, chunk in enumerate(sub_chunks):
                    if chunk.strip():
                        # 使用顺序匹配器查找精确页码
                        match_result = sequential_matcher.find_next_page_match(chunk)
                        
                        if match_result:
                            chunk_page_number = match_result['page_number']
                            is_exact_match = True
                            match_info = match_result
                        else:
                            # 回退到标题页码
                            chunk_page_number = page_numbers[0] if page_numbers else 1
                            is_exact_match = False
                            match_info = None
                        
                        metadata = ChunkMetadata(
                            project_name=project_name,
                            page_number=chunk_page_number,  # 使用段落的精确页码
                            chunk_type='title',
                            title_level=title_level,
                            title_content=title_content,
                            chunk_index=i,
                            total_chunks=len(sub_chunks),
                            text_length=len(chunk)
                        )
                        
                        # 合并原始 metadata 和我们的 metadata
                        final_metadata = metadata.__dict__.copy()
                        final_metadata.update(md_doc.metadata)  # 保留 MarkdownHeaderTextSplitter 的元数据
                        
                        # 添加页码相关信息
                        final_metadata['title_page_numbers'] = page_numbers  # 标题覆盖的页码范围
                        final_metadata['spans_multiple_pages'] = len(page_numbers) > 1
                        final_metadata['is_exact_page_match'] = is_exact_match
                        if match_info:
                            final_metadata['match_info'] = match_info
                        
                        doc = Document(
                            page_content=chunk,
                            metadata=final_metadata
                        )
                        documents.append(doc)
        
        except Exception as e:
            logger.warning(f"使用 MarkdownHeaderTextSplitter 分块失败，回退到基础文本分割: {e}")
            # 回退方案：使用基础文本分割，也使用顺序匹配器
            chunks = self.text_splitter.split_text(content)
            for i, chunk in enumerate(chunks):
                if chunk.strip():
                    # 在回退情况下也使用顺序匹配器
                    match_result = sequential_matcher.find_next_page_match(chunk)
                    
                    if match_result:
                        chunk_page_number = match_result['page_number']
                        is_exact_match = True
                        match_info = match_result
                    else:
                        chunk_page_number = 1
                        is_exact_match = False
                        match_info = None
                    
                    metadata = ChunkMetadata(
                        project_name=project_name,
                        page_number=chunk_page_number,
                        chunk_type='title',
                        title_level=0,
                        title_content="",
                        chunk_index=i,
                        total_chunks=len(chunks),
                        text_length=len(chunk)
                    )
                    
                    final_metadata = metadata.__dict__.copy()
                    final_metadata['title_page_numbers'] = [1]  # 回退情况下的页码范围
                    final_metadata['spans_multiple_pages'] = False
                    final_metadata['is_exact_page_match'] = is_exact_match
                    if match_info:
                        final_metadata['match_info'] = match_info
                    
                    doc = Document(
                        page_content=chunk,
                        metadata=final_metadata
                    )
                    documents.append(doc)
        
        # 显示顺序匹配的最终统计
        progress_info = sequential_matcher.get_progress_info()
        logger.info(f"📊 顺序匹配完成统计:")
        logger.info(f"  ✅ 成功匹配: {progress_info['matched_count']} 个段落")
        logger.info(f"  ⏭️ 跳过文本块: {progress_info['skipped_count']} 个")
        logger.info(f"  📍 最终位置: {progress_info['current_position']}/{progress_info['total_blocks']} ({progress_info['progress_percent']}%)")
        logger.info(f"  🔄 剩余未用: {progress_info['remaining_blocks']} 个文本块")
        
        # 分析剩余文本块和匹配失败的原因
        if progress_info['remaining_blocks'] > progress_info['total_blocks'] * 0.3:
            logger.warning(f"⚠️ 剩余文本块较多({progress_info['remaining_blocks']}个)，可能存在以下情况:")
            logger.warning("   1. MD文件开头部分被删除但content_list.json中仍存在（如目录信息）")
            logger.warning("   2. MD文件结尾部分被删除")
            logger.warning("   3. MD文件中插入了新内容，导致顺序错位")
            logger.warning("   4. 匹配参数需要调优（搜索窗口、覆盖率阈值）")
        
        total_chunks = len(documents) if documents else 0
        match_rate = progress_info['matched_count'] / total_chunks if total_chunks > 0 else 0
        if match_rate < 0.5:
            logger.error(f"❌ 顺序匹配率过低({match_rate*100:.1f}%)，建议:")
            logger.error("   1. 增大搜索窗口（当前20）→ 30-50")
            logger.error("   2. 降低覆盖率阈值（当前80%）→ 70%")
            logger.error("   3. 启用调试模式查看具体匹配详情")
            logger.error("   4. 检查MD和JSON文件的内容一致性")
        elif match_rate < 0.7:
            logger.warning(f"⚠️ 顺序匹配率偏低({match_rate*100:.1f}%)，可考虑微调参数优化")
        
        return documents
    
    def build_title_page_mapping(self, metadata_list: List[Dict]) -> Dict[str, List[int]]:
        """从content_list.json构建标题到页码的映射表"""
        title_page_mapping = {}
        
        for item in metadata_list:
            if item.get('type') == 'text' and 'text_level' in item:
                # 这是一个标题
                title_text = item['text'].strip()
                page_idx = item.get('page_idx', 0)
                page_number = page_idx + 1  # 修正页码（0基转1基）
                
                if title_text:
                    if title_text not in title_page_mapping:
                        title_page_mapping[title_text] = []
                    
                    # 避免重复页码
                    if page_number not in title_page_mapping[title_text]:
                        title_page_mapping[title_text].append(page_number)
        
        # 对页码进行排序
        for title in title_page_mapping:
            title_page_mapping[title].sort()
        
        logger.info(f"构建了 {len(title_page_mapping)} 个标题的页码映射")
        return title_page_mapping
    
    def find_pages_for_title(self, title_content: str, title_page_mapping: Dict[str, List[int]]) -> List[int]:
        """为给定标题查找对应的页码"""
        if not title_content:
            return [1]
        
        # 直接匹配
        if title_content in title_page_mapping:
            return title_page_mapping[title_content]
        
        # 模糊匹配：查找包含关键词的标题
        title_words = set(title_content.lower().split())
        best_match = None
        best_score = 0
        
        for mapped_title, pages in title_page_mapping.items():
            mapped_words = set(mapped_title.lower().split())
            # 计算词汇交集
            intersection = title_words & mapped_words
            if intersection:
                score = len(intersection) / len(title_words | mapped_words)
                if score > best_score and score > 0.3:  # 至少30%的词汇匹配
                    best_score = score
                    best_match = pages
        
        return best_match if best_match else [1]
    


class VectorIndexBuilder:
    """向量索引构建器"""
    
    def __init__(self):
        self.setup_embedding_model()
        self.chunker = DocumentChunker()
    
    def setup_embedding_model(self):
        """设置嵌入模型"""
        # 从环境变量获取配置
        embedding_model = os.getenv('EMBEDDING_MODEL', 'Qwen/Qwen3-Embedding-8B')
        cn_base_url = os.getenv('CN_BASE_URL', 'https://api.siliconflow.cn/v1')
        cn_api_key = os.getenv('CN_API_KEY')
        
        if not cn_api_key:
            raise ValueError("请在 .env 文件中设置 CN_API_KEY")
        
        try:
            # 使用 OpenAI 兼容的嵌入模型
            self.embeddings = OpenAIEmbeddings(
                model=embedding_model,
                openai_api_base=cn_base_url,
                openai_api_key=cn_api_key
            )
            logger.info(f"成功初始化嵌入模型: {embedding_model}")
        except Exception as e:
            logger.error(f"初始化嵌入模型失败: {e}")
            # 备用方案：使用本地 HuggingFace 模型
            logger.info("尝试使用本地 HuggingFace 模型...")
            self.embeddings = HuggingFaceEmbeddings(
                model_name="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
            )
            logger.info("成功初始化备用嵌入模型")
    
    def generate_safe_folder_name(self, original_name: str) -> str:
        """生成安全的文件夹名称"""
        # 使用MD5哈希生成唯一且安全的文件夹名
        hash_object = hashlib.md5(original_name.encode('utf-8'))
        safe_name = hash_object.hexdigest()
        
        # 添加前缀以便识别
        return f"project_{safe_name}"
    
    def create_vector_store_with_batching(self, documents: List[Document], batch_size: int = 100) -> Optional[FAISS]:
        """简化的批量向量存储创建"""
        if not documents:
            logger.warning("没有文档用于创建向量存储")
            return None
        
        # 如果文档数量不多（≤200），直接处理
        if len(documents) <= 200:
            try:
                return FAISS.from_documents(documents, self.embeddings)
            except Exception as e:
                logger.error(f"创建向量存储失败: {e}")
                return None
        
        # 文档数量超过200，分批处理
        logger.info(f"文档数量 {len(documents)} 较多，使用批次大小 {batch_size} 进行分批处理")
        
        vector_store = None
        success_count = 0
        
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(documents) + batch_size - 1) // batch_size
            
            try:
                if vector_store is None:
                    # 创建第一个向量存储
                    vector_store = FAISS.from_documents(batch_docs, self.embeddings)
                else:
                    # 创建批次向量存储并合并
                    batch_store = FAISS.from_documents(batch_docs, self.embeddings)
                    vector_store.merge_from(batch_store)
                
                success_count += len(batch_docs)
                logger.info(f"批次 {batch_num}/{total_batches} 成功，已处理 {success_count} 个文档")
                
                # 批次间短暂休息，避免API限流
                if i + batch_size < len(documents):
                    time.sleep(1)
                    
            except Exception as e:
                logger.warning(f"批次 {batch_num} 失败: {e}，跳过此批次")
                continue
        
        if vector_store is not None:
            logger.info(f"批量处理完成，成功处理 {success_count}/{len(documents)} 个文档")
        else:
            logger.error("所有批次都处理失败")
        
        return vector_store
    
    def load_project_data(self, project_dir: Path) -> Tuple[str, List[Dict], str, str]:
        """加载项目数据"""
        # 读取 full.md
        full_md_path = project_dir / "full.md"
        if not full_md_path.exists():
            raise FileNotFoundError(f"未找到 full.md 文件: {full_md_path}")
        
        with open(full_md_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        # 查找 *_content_list.json 文件
        json_files = list(project_dir.glob("*_content_list.json"))
        if not json_files:
            raise FileNotFoundError(f"未找到 *_content_list.json 文件: {project_dir}")
        
        json_file = json_files[0]  # 使用第一个匹配的文件
        with open(json_file, 'r', encoding='utf-8') as f:
            metadata_list = json.load(f)
        
        # 原始项目名称（中文）
        original_project_name = project_dir.name
        # 安全的文件夹名称（英文）
        safe_folder_name = self.generate_safe_folder_name(original_project_name)
        
        return markdown_content, metadata_list, original_project_name, safe_folder_name
    
    def build_index_for_project(self, project_dir: Path, output_dir: Path, chunk_strategy: str = 'both', force_rebuild: bool = False):
        """为单个项目构建向量索引"""
        logger.info(f"开始处理项目: {project_dir.name}")
        
        try:
            # 加载项目数据
            markdown_content, metadata_list, original_project_name, safe_folder_name = self.load_project_data(project_dir)
            
            # 使用安全的文件夹名作为存储路径
            project_db_dir = output_dir / safe_folder_name
            if project_db_dir.exists() and not force_rebuild:
                # 检查数据库文件完整性
                faiss_file = project_db_dir / "index.faiss"
                pkl_file = project_db_dir / "index.pkl"
                metadata_file = project_db_dir / "metadata.json"
                
                if all(f.exists() for f in [faiss_file, pkl_file, metadata_file]):
                    logger.info(f"项目 {original_project_name} 的向量数据库已存在，跳过构建")
                    return True
                else:
                    logger.warning(f"项目 {original_project_name} 的向量数据库不完整，重新构建")
            elif project_db_dir.exists() and force_rebuild:
                logger.info(f"强制重建项目 {original_project_name} 的向量数据库")
            
            # 使用优化的标题分块策略（包含准确页码信息）
            logger.info("执行标题分块（包含页码信息）...")
            all_documents = self.chunker.chunk_by_title_with_pages(markdown_content, metadata_list, original_project_name)
            
            # 统计页码匹配情况
            exact_matches = len([d for d in all_documents if d.metadata.get('is_exact_page_match', False)])
            title_matches = len(all_documents) - exact_matches
            spans_multiple = len([d for d in all_documents if d.metadata.get('spans_multiple_pages', False)])
            
            logger.info(f"标题分块生成 {len(all_documents)} 个文档块")
            logger.info(f"📊 页码匹配统计:")
            logger.info(f"  ✅ 段落精确匹配: {exact_matches} 个 ({exact_matches/len(all_documents)*100:.1f}%)")
            logger.info(f"  📄 使用标题页码: {title_matches} 个 ({title_matches/len(all_documents)*100:.1f}%)")
            logger.info(f"  📚 跨页标题: {spans_multiple} 个")
            
            # 根据匹配率给出不同的建议
            exact_match_rate = exact_matches / len(all_documents)
            if exact_match_rate < 0.3:
                logger.error(f"❌ 段落精确匹配率过低({exact_match_rate*100:.1f}%)，建议:")
                logger.error("   1. 检查content_list.json的文本内容是否与markdown内容一致")
                logger.error("   2. 降低匹配阈值(当前0.3)或改进匹配算法")
                logger.error("   3. 检查文档是否存在大量表格、图片等非文本内容")
            elif exact_match_rate < 0.5:
                logger.warning(f"⚠️ 段落精确匹配率偏低({exact_match_rate*100:.1f}%)，建议:")
                logger.warning("   1. 可考虑微调匹配阈值优化匹配效果") 
                logger.warning("   2. 检查是否存在过多的表格、公式等特殊内容")
            else:
                logger.info(f"✅ 段落精确匹配率良好({exact_match_rate*100:.1f}%)")
            
            if not all_documents:
                logger.warning(f"项目 {original_project_name} 没有生成任何文档块")
                return
            
            logger.info(f"总计生成 {len(all_documents)} 个文档块")
            
            # 构建 FAISS 向量索引
            logger.info("开始构建 FAISS 向量索引...")
            
            # 超过200个文档时使用分批处理避免API限制
            vector_store = self.create_vector_store_with_batching(all_documents)
            
            if vector_store is None:
                logger.error(f"无法为项目 {original_project_name} 创建向量索引")
                return False
            
            # 保存向量索引
            project_db_dir.mkdir(parents=True, exist_ok=True)
            
            vector_store.save_local(str(project_db_dir))
            logger.info(f"向量索引已保存到: {project_db_dir}")
            
            # 保存项目元数据
            project_metadata = {
                'project_name': original_project_name,  # 保存原始中文项目名
                'safe_folder_name': safe_folder_name,  # 保存安全文件夹名
                'total_documents': len(all_documents),
                'chunk_strategy': 'title_with_exact_pages',  # 新的精确页码分块策略
                'title_chunks': len(all_documents),  # 所有文档都是标题分块
                'spans_multiple_pages': spans_multiple,
                'exact_page_matches': exact_matches,
                'title_page_matches': title_matches,
                'exact_match_rate': round(exact_matches/len(all_documents)*100, 1) if all_documents else 0,
                'embedding_model': os.getenv('EMBEDDING_MODEL', 'Qwen/Qwen3-Embedding-8B'),
                'source_dir': str(project_dir),
                'match_strategy': 'sequential_matching_v2',  # 匹配策略说明
                'sequential_order': True,  # 使用顺序匹配
                'exact_inclusion_priority': True,  # 优先使用精确文本包含
                'table_content_support': True,  # 支持表格内容匹配
                'metadata_content_support': True,  # 支持元数据内容匹配
                'coverage_thresholds': {
                    'standard': 0.8,   # 标准内容80%
                    'table': 0.6,      # 表格内容60%
                    'metadata': 0.5     # 元数据内容50%
                },
                'search_window': 20,  # 每次搜索窗口大小
                'end_of_file_optimization': True  # 文件末尾优化
            }
            
            metadata_file = project_db_dir / 'metadata.json'
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(project_metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"项目 {original_project_name} 处理完成")
            return True
            
        except Exception as e:
            logger.error(f"处理项目 {project_dir.name} 时出错: {e}")
            raise
    
    def build_all_indexes(self, parser_dir: Path, output_dir: Path, chunk_strategy: str = 'both', force_rebuild: bool = False, max_workers: int = None):
        """为所有项目构建向量索引"""
        logger.info(f"开始批量构建向量索引...")
        logger.info(f"解析目录: {parser_dir}")
        logger.info(f"输出目录: {output_dir}")
        logger.info(f"分块策略: {chunk_strategy}")
        logger.info(f"强制重建: {'是' if force_rebuild else '否'}")
        
        if not parser_dir.exists():
            raise FileNotFoundError(f"解析目录不存在: {parser_dir}")
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取所有项目目录
        project_dirs = [d for d in parser_dir.iterdir() if d.is_dir()]
        logger.info(f"找到 {len(project_dirs)} 个项目目录")
        
        # 检查已存在的数据库
        if not force_rebuild:
            existing_dbs = []
            missing_dbs = []
            
            for project_dir in project_dirs:
                project_db_dir = output_dir / project_dir.name
                if project_db_dir.exists():
                    faiss_file = project_db_dir / "index.faiss"
                    pkl_file = project_db_dir / "index.pkl"
                    metadata_file = project_db_dir / "metadata.json"
                    
                    if all(f.exists() for f in [faiss_file, pkl_file, metadata_file]):
                        existing_dbs.append(project_dir.name)
                    else:
                        missing_dbs.append(project_dir.name)
                else:
                    missing_dbs.append(project_dir.name)
            
            logger.info(f"已存在完整数据库: {len(existing_dbs)} 个")
            logger.info(f"需要构建的项目: {len(missing_dbs)} 个")
            
            if existing_dbs:
                logger.info("已存在的数据库将被跳过，如需重建请使用强制重建模式")
        
        success_count = 0
        skipped_count = 0
        failed_projects = []

        # 确定工作进程数
        if max_workers is None:
            max_workers = min(4, multiprocessing.cpu_count())  # 默认最多4个进程

        logger.info(f"使用 {max_workers} 个进程并行处理")

        if max_workers == 1:
            # 单进程模式（原有逻辑）
            for project_dir in project_dirs:
                try:
                    result = self.build_index_for_project(project_dir, output_dir, chunk_strategy, force_rebuild)
                    if result is True:
                        success_count += 1
                    else:
                        skipped_count += 1
                except Exception as e:
                    logger.error(f"项目 {project_dir.name} 处理失败: {e}")
                    failed_projects.append(project_dir.name)
        else:
            # 多进程模式
            # 准备任务参数
            tasks = [(str(project_dir), str(output_dir), chunk_strategy, force_rebuild)
                    for project_dir in project_dirs]

            # 使用进程池处理
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_project = {
                    executor.submit(process_single_project_worker, task): task[0]
                    for task in tasks
                }

                # 收集结果
                for future in as_completed(future_to_project):
                    project_path = future_to_project[future]
                    project_name = Path(project_path).name

                    try:
                        project_name, success, error_msg = future.result()
                        if success:
                            success_count += 1
                            logger.info(f"✅ 项目 {project_name} 处理成功")
                        else:
                            if error_msg and "项目被跳过" not in error_msg:
                                failed_projects.append(project_name)
                                logger.error(f"❌ 项目 {project_name} 处理失败: {error_msg}")
                            else:
                                skipped_count += 1
                                logger.info(f"⏭️  项目 {project_name} 被跳过")
                    except Exception as e:
                        failed_projects.append(project_name)
                        logger.error(f"❌ 项目 {project_name} 处理异常: {e}")
        
        # 输出总结
        logger.info("=" * 50)
        logger.info("批量构建完成！")
        logger.info(f"成功构建: {success_count} 个项目")
        logger.info(f"跳过已存在: {skipped_count} 个项目")
        logger.info(f"失败项目: {len(failed_projects)} 个")
        
        if failed_projects:
            logger.warning("失败的项目:")
            for project in failed_projects:
                logger.warning(f"  - {project}")
        
        # 保存全局统计信息
        global_stats = {
            'total_projects': len(project_dirs),
            'success_count': success_count,
            'skipped_count': skipped_count,
            'failed_count': len(failed_projects),
            'failed_projects': failed_projects,
            'chunk_strategy': chunk_strategy,
            'force_rebuild': force_rebuild,
            'embedding_model': os.getenv('EMBEDDING_MODEL', 'Qwen/Qwen3-Embedding-8B'),
            'build_time': str(Path().cwd())
        }
        
        stats_file = output_dir / 'build_stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(global_stats, f, ensure_ascii=False, indent=2)
        
        logger.info(f"构建统计信息已保存到: {stats_file}")

def process_single_project_worker(args):
    """
    多进程工作函数：处理单个项目

    Args:
        args: (project_dir_str, output_dir_str, chunk_strategy, force_rebuild)

    Returns:
        (project_name, success, error_msg)
    """
    project_dir_str, output_dir_str, chunk_strategy, force_rebuild = args
    project_dir = Path(project_dir_str)
    output_dir = Path(output_dir_str)

    # 为每个进程设置独立的日志
    process_logger = logging.getLogger(f"worker_{os.getpid()}")

    try:
        # 重新加载环境变量（每个进程独立）
        load_dotenv()

        # 创建新的构建器实例（每个进程独立）
        builder = VectorIndexBuilder()

        # 处理项目
        result = builder.build_index_for_project(project_dir, output_dir, chunk_strategy, force_rebuild)

        if result is True:
            return (project_dir.name, True, None)
        else:
            return (project_dir.name, False, "项目被跳过")

    except Exception as e:
        error_msg = f"处理项目 {project_dir.name} 时出错: {str(e)}"
        process_logger.error(error_msg)
        return (project_dir.name, False, error_msg)

def main():
    """主函数"""
    # 配置路径
    current_dir = Path.cwd()
    parser_dir = current_dir / "data" / "parser"
    output_dir = current_dir / "data" / "db"
    
    # 检查环境变量
    required_env_vars = ['CN_API_KEY']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {missing_vars}")
        logger.error("请在 .env 文件中设置这些变量")
        return
    
    # 检查是否已存在向量数据库
    force_rebuild = False
    if output_dir.exists():
        existing_projects = [d for d in output_dir.iterdir() if d.is_dir()]
        if existing_projects:
            logger.info(f"检测到已存在 {len(existing_projects)} 个向量数据库：")
            for project in existing_projects[:5]:  # 只显示前5个
                logger.info(f"  - {project.name}")
            if len(existing_projects) > 5:
                logger.info(f"  ... 还有 {len(existing_projects) - 5} 个项目")
            
            print("\n请选择构建模式：")
            print("1. 断点续传 - 只构建缺失的项目（推荐）")
            print("2. 强制重建 - 重建所有项目")
            print("3. 退出")

            while True:
                choice = input("\n请输入选择 (1-3): ").strip()
                if choice == '1':
                    force_rebuild = False
                    logger.info("选择断点续传模式")
                    break
                elif choice == '2':
                    force_rebuild = True
                    logger.info("选择强制重建模式")
                    break
                elif choice == '3':
                    logger.info("用户选择退出")
                    return
                else:
                    print("无效选择，请输入 1、2 或 3")
    else:
        logger.info("未检测到已存在的向量数据库，将进行全新构建")

    # 选择进程数
    max_cpu = multiprocessing.cpu_count()
    print(f"\n检测到 {max_cpu} 个CPU核心")
    print("请选择并行进程数：")
    print("1. 单进程（稳定，适合调试）")
    print(f"2. 2个进程（推荐）")
    print(f"3. 4个进程（快速）")
    print(f"4. 自定义进程数")

    max_workers = 1  # 默认单进程
    while True:
        choice = input(f"\n请输入选择 (1-4): ").strip()
        if choice == '1':
            max_workers = 1
            logger.info("选择单进程模式")
            break
        elif choice == '2':
            max_workers = 2
            logger.info("选择2进程模式")
            break
        elif choice == '3':
            max_workers = 4
            logger.info("选择4进程模式")
            break
        elif choice == '4':
            try:
                max_workers = int(input(f"请输入进程数 (1-{max_cpu}): ").strip())
                if 1 <= max_workers <= max_cpu:
                    logger.info(f"选择{max_workers}进程模式")
                    break
                else:
                    print(f"进程数必须在 1-{max_cpu} 之间")
            except ValueError:
                print("请输入有效的数字")
        else:
            print("无效选择，请输入 1、2、3 或 4")

    # 创建构建器
    builder = VectorIndexBuilder()

    # 使用优化的分块策略（标题分块 + 页码信息）
    chunk_strategy = 'title_with_pages'
    logger.info(f"使用分块策略: {chunk_strategy} (标题分块 + 顺序页码匹配)")

    try:
        # 构建所有项目的向量索引
        builder.build_all_indexes(parser_dir, output_dir, chunk_strategy, force_rebuild, max_workers)

        logger.info("所有向量索引构建完成！")

    except Exception as e:
        logger.error(f"构建过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()