#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重排序模块

功能特性：
1. 使用 Qwen/Qwen3-Reranker-8B 模型对检索结果进行重排序
2. 提高检索结果的相关性和准确性
3. 支持批量重排序和错误处理

作者: AI Assistant
日期: 2025年
"""

import os
import logging
from typing import List, Dict, Any, Tuple
import requests
import json
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

class Reranker:
    """重排序器，使用 Qwen/Qwen3-Reranker-8B 模型"""
    
    def __init__(self):
        self.setup_reranker()
    
    def setup_reranker(self):
        """设置重排序模型"""
        self.rerank_model = os.getenv('CN_Rerank_MODEL', 'Qwen/Qwen3-Reranker-8B')
        self.base_url = os.getenv('CN_BASE_URL', 'https://api.siliconflow.cn/v1')
        self.api_key = os.getenv('CN_API_KEY')
        
        if not self.api_key:
            logger.warning("未设置 CN_API_KEY，重排序功能将被禁用")
            self.enabled = False
        else:
            self.enabled = True
            logger.info(f"重排序器已初始化，使用模型: {self.rerank_model}")
    
    def rerank(self, query: str, documents: List[Dict[str, Any]], top_k: int = 10) -> List[Dict[str, Any]]:
        """
        对文档进行重排序
        
        Args:
            query: 查询文本
            documents: 文档列表，每个文档包含 content 和其他元数据
            top_k: 返回的文档数量
            
        Returns:
            重排序后的文档列表，按相关性降序排列
        """
        if not self.enabled:
            logger.warning("重排序功能未启用，返回原始结果")
            return documents[:top_k]
        
        if not documents:
            return []
        
        try:
            # 准备重排序请求
            texts = [doc.get('content', '') for doc in documents]
            
            # 调用重排序API
            scores = self._call_rerank_api(query, texts)
            
            if scores is None:
                logger.warning("重排序失败，返回原始结果")
                return documents[:top_k]
            
            # 将分数与文档配对并排序
            doc_scores = list(zip(documents, scores))
            doc_scores.sort(key=lambda x: x[1], reverse=True)
            
            # 添加重排序分数到文档元数据
            reranked_docs = []
            for doc, score in doc_scores[:top_k]:
                doc_copy = doc.copy()
                doc_copy['rerank_score'] = score
                reranked_docs.append(doc_copy)
            
            logger.info(f"重排序完成，返回 {len(reranked_docs)} 个文档")
            return reranked_docs
            
        except Exception as e:
            logger.error(f"重排序过程中出现错误: {e}")
            return documents[:top_k]
    
    def _call_rerank_api(self, query: str, texts: List[str], max_retries: int = 3) -> List[float]:
        """
        调用重排序API
        
        Args:
            query: 查询文本
            texts: 文档文本列表
            max_retries: 最大重试次数
            
        Returns:
            相关性分数列表
        """
        url = f"{self.base_url}/rerank"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.rerank_model,
            "query": query,
            "documents": texts,
            "return_documents": False,
            "top_k": len(texts)  # 返回所有文档的分数
        }
        
        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=headers, json=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 提取分数
                    if 'results' in result:
                        scores = [item.get('relevance_score', 0.0) for item in result['results']]
                        return scores
                    else:
                        logger.error(f"重排序API返回格式错误: {result}")
                        return None
                        
                elif response.status_code == 429:
                    # 速率限制，等待后重试
                    wait_time = 2 ** attempt
                    logger.warning(f"API速率限制，等待 {wait_time} 秒后重试")
                    time.sleep(wait_time)
                    continue
                    
                else:
                    logger.error(f"重排序API调用失败: {response.status_code}, {response.text}")
                    return None
                    
            except requests.exceptions.Timeout:
                logger.warning(f"重排序API调用超时，尝试 {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue
                    
            except Exception as e:
                logger.error(f"重排序API调用异常: {e}")
                return None
        
        logger.error("重排序API调用失败，已达到最大重试次数")
        return None
    
    def batch_rerank(self, queries_docs: List[Tuple[str, List[Dict[str, Any]]]], top_k: int = 10) -> List[List[Dict[str, Any]]]:
        """
        批量重排序
        
        Args:
            queries_docs: (查询, 文档列表) 的列表
            top_k: 每个查询返回的文档数量
            
        Returns:
            重排序后的文档列表的列表
        """
        results = []
        for query, docs in queries_docs:
            reranked = self.rerank(query, docs, top_k)
            results.append(reranked)
        
        return results

def main():
    """测试重排序功能"""
    # 创建重排序器
    reranker = Reranker()
    
    if not reranker.enabled:
        print("重排序功能未启用，请检查API配置")
        return
    
    # 测试数据
    query = "广联达的BIM技术应用"
    documents = [
        {
            "content": "广联达是中国领先的数字建筑平台服务商，专注于BIM技术的研发和应用。",
            "metadata": {"source": "doc1"}
        },
        {
            "content": "建筑信息模型（BIM）是一种数字化的建筑设计和管理方法。",
            "metadata": {"source": "doc2"}
        },
        {
            "content": "广联达的数字项目管理平台帮助施工企业提升数字化转型能力。",
            "metadata": {"source": "doc3"}
        }
    ]
    
    print(f"查询: {query}")
    print(f"原始文档数量: {len(documents)}")
    
    # 执行重排序
    reranked_docs = reranker.rerank(query, documents, top_k=3)
    
    print(f"\n重排序结果:")
    for i, doc in enumerate(reranked_docs, 1):
        score = doc.get('rerank_score', 'N/A')
        content = doc['content'][:100] + "..." if len(doc['content']) > 100 else doc['content']
        print(f"{i}. 分数: {score:.4f} - {content}")

if __name__ == "__main__":
    main()
