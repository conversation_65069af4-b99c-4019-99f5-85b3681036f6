#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成数据库映射文件

功能：
1. 扫描data/db目录下的所有数据库文件夹
2. 读取每个数据库的metadata.json文件
3. 生成数据库文件夹名称到实际项目名称的映射
4. 提取公司名称用于智能匹配

作者: AI Assistant
日期: 2025年
"""

import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)



def generate_db_mapping():
    """生成数据库映射文件"""
    logger.info("开始生成数据库映射文件...")
    
    # 配置路径
    current_dir = Path.cwd()
    db_dir = current_dir / "data" / "db"
    output_file = db_dir / "db_mapping.json"
    
    if not db_dir.exists():
        logger.error(f"数据库目录不存在: {db_dir}")
        return False
    
    mapping_data = {}  # 简单的文件夹名 -> 项目名映射
    
    total_dbs = 0
    successful_dbs = 0
    
    # 遍历所有数据库文件夹
    for db_path in db_dir.iterdir():
        if not db_path.is_dir():
            continue
            
        total_dbs += 1
        db_name = db_path.name
        metadata_file = db_path / "metadata.json"
        
        if not metadata_file.exists():
            logger.warning(f"数据库 {db_name} 缺少 metadata.json 文件")
            continue
        
        try:
            # 读取元数据
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            project_name = metadata.get('project_name', db_name)

            # 简单映射：文件夹名 -> 项目名
            mapping_data[db_name] = project_name
            
            successful_dbs += 1
            logger.debug(f"处理数据库: {db_name} -> {project_name}")
            
        except Exception as e:
            logger.error(f"处理数据库 {db_name} 时出错: {e}")
    
    # 不需要额外的统计信息，直接保存映射
    
    # 保存映射文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据库映射文件已生成: {output_file}")
        logger.info(f"统计信息:")
        logger.info(f"  总数据库数: {total_dbs}")
        logger.info(f"  成功处理: {successful_dbs}")
        logger.info(f"  失败数据库: {total_dbs - successful_dbs}")

        # 显示部分映射示例
        sample_mappings = list(mapping_data.items())[:5]
        logger.info(f"  映射示例:")
        for db_name, project_name in sample_mappings:
            logger.info(f"    {db_name} -> {project_name[:50]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"保存映射文件失败: {e}")
        return False

def show_mapping_stats():
    """显示映射统计信息"""
    current_dir = Path.cwd()
    db_dir = current_dir / "data" / "db"
    mapping_file = db_dir / "db_mapping.json"

    if not mapping_file.exists():
        logger.error("映射文件不存在，请先运行生成脚本")
        return

    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)

        print("=" * 60)
        print("数据库映射统计信息")
        print("=" * 60)
        print(f"总数据库数: {len(mapping_data)}")

        print("\n映射示例 (前10个):")
        for i, (db_name, project_name) in enumerate(list(mapping_data.items())[:10], 1):
            print(f"  {i:2d}. {db_name}")
            print(f"      -> {project_name}")

        if len(mapping_data) > 10:
            print(f"  ... 还有 {len(mapping_data) - 10} 个数据库")

    except Exception as e:
        logger.error(f"读取映射文件失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='生成数据库映射文件')
    parser.add_argument('--generate', action='store_true', help='生成映射文件')
    parser.add_argument('--stats', action='store_true', help='显示统计信息')
    
    args = parser.parse_args()
    
    if args.generate:
        success = generate_db_mapping()
        if success:
            print("✓ 数据库映射文件生成成功")
        else:
            print("✗ 数据库映射文件生成失败")
    
    elif args.stats:
        show_mapping_stats()
    
    else:
        # 默认生成映射文件
        success = generate_db_mapping()
        if success:
            print("\n")
            show_mapping_stats()

if __name__ == "__main__":
    main()
