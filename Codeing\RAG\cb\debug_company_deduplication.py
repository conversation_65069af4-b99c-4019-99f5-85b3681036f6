#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试公司去重逻辑 - 分析是否过度去重导致丢失重要文档
"""

import os
import json
import logging
from pathlib import Path
from dotenv import load_dotenv

# 导入数据库匹配器
from modules.vector_retriever import DatabaseMatcher

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_company_deduplication():
    """分析公司去重是否过度，导致重要文档丢失"""
    
    print("=" * 80)
    print("公司去重逻辑分析")
    print("=" * 80)
    
    # 初始化匹配器
    db_dir = Path("data/db")
    matcher = DatabaseMatcher(db_dir)
    
    # 分析广联达和伊利股份的项目数量
    all_projects = list(matcher.db_metadata.items())
    
    # 统计各公司的项目数量
    company_projects = {}
    for db_name, metadata in all_projects:
        project_name = metadata.get('project_name', db_name)
        company_key = project_name.split('-')[0].strip()
        
        if company_key not in company_projects:
            company_projects[company_key] = []
        company_projects[company_key].append({
            'db_name': db_name,
            'project_name': project_name
        })
    
    # 重点分析问题公司
    problem_companies = ['广联达', '伊利股份', '凌云股份']
    
    for company in problem_companies:
        print(f"\n🔍 {company} 项目分析:")
        
        if company in company_projects:
            projects = company_projects[company]
            print(f"   总项目数: {len(projects)}")
            print(f"   去重后代表: {len([info for info in matcher.company_representatives.values() if info['company_name'] == company])}")
            
            print(f"   📋 所有项目:")
            for i, proj in enumerate(projects):
                is_representative = any(
                    info['db_name'] == proj['db_name'] 
                    for info in matcher.company_representatives.values()
                )
                status = "★ 代表项目" if is_representative else "  普通项目"
                print(f"      {i+1}. {status} {proj['project_name'][:80]}...")
        else:
            print(f"   ❌ 未找到该公司的项目")
    
    # 分析去重前后的总数变化
    print(f"\n📊 整体去重统计:")
    print(f"   去重前总项目数: {len(all_projects)}")
    print(f"   去重后代表数: {len(matcher.company_representatives)}")
    print(f"   丢失项目数: {len(all_projects) - len(matcher.company_representatives)}")
    print(f"   去重比例: {(len(all_projects) - len(matcher.company_representatives)) / len(all_projects) * 100:.1f}%")

def test_specific_failed_queries():
    """测试具体失败的查询，看是否是去重导致的问题"""
    
    print("\n" + "=" * 80)
    print("失败查询分析 - 验证去重影响")
    print("=" * 80)
    
    # 初始化匹配器
    db_dir = Path("data/db")
    matcher = DatabaseMatcher(db_dir)
    
    # 具体的失败案例
    failed_cases = [
        {
            "query": "如何分析广联达在2006年至2020年间营业收入和净利润的增长趋势？",
            "expected": "广联达-再谈广联达当前时点下如何看待其三条增长曲线-*********页",
            "actual_top1": "广联达-数字经济SaaS专题广联达深度报告数字建筑领军战略驱动成长-2022040131页"
        },
        {
            "query": "关于伊利股份（600887）的深度报告《王者荣耀，行稳致远》，请问在2009年之后，伊利是如何通过营销策略实现了后来居上？",
            "expected": "伊利股份-公司深度报告王者荣耀行稳致远-2020070631页",
            "actual_top1": "伊利股份内蒙古伊利实业集团股份有限公司2024年第三季度报告"
        }
    ]
    
    for i, case in enumerate(failed_cases):
        print(f"\n{i+1}. 查询: {case['query'][:50]}...")
        print(f"   期望项目: {case['expected']}")
        print(f"   实际TOP1: {case['actual_top1']}")
        
        # 检查期望项目是否在原始数据库中存在
        expected_exists = False
        expected_is_representative = False
        
        for db_name, metadata in matcher.db_metadata.items():
            project_name = metadata.get('project_name', db_name)
            if case['expected'] in project_name:
                expected_exists = True
                # 检查是否是代表项目
                for info in matcher.company_representatives.values():
                    if info['project_name'] == project_name:
                        expected_is_representative = True
                        break
                break
        
        print(f"   期望项目存在: {'✅' if expected_exists else '❌'}")
        print(f"   期望项目是代表: {'✅' if expected_is_representative else '❌'}")
        
        if expected_exists and not expected_is_representative:
            print(f"   ⚠️  问题确认: 期望项目存在但被去重排除！")

def propose_solution():
    """提出解决方案"""
    
    print("\n" + "=" * 80)
    print("问题解决方案")
    print("=" * 80)
    
    print("🎯 核心问题: 公司去重过度，导致重要文档丢失")
    print("\n💡 可能的解决方案:")
    print("   1. 完全取消公司去重 - 保留所有项目进行匹配")
    print("   2. 改进去重策略 - 每个公司保留TOP3而不是TOP1")
    print("   3. 智能去重 - 基于项目重要性/时效性选择代表")
    print("   4. 分层匹配 - 先匹配公司，再在公司内匹配具体项目")
    
    print("\n🚀 推荐方案: 先尝试 **完全取消去重**，看整体效果")
    print("   - 优点: 简单直接，不丢失任何文档")
    print("   - 缺点: 可能会有一定的计算开销")
    print("   - 评估: 132个项目对于现代计算来说不算大，可以接受")

if __name__ == "__main__":
    analyze_company_deduplication()
    test_specific_failed_queries()
    propose_solution()