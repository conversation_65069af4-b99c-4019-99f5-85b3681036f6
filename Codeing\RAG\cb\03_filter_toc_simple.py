#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版目录过滤器

过滤parser目录下文档中的目录部分，支持更灵活的目录标题匹配

作者: AI Assistant
日期: 2025年
"""

import os
import re
import logging
from pathlib import Path
from typing import List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleTableOfContentsFilter:
    """简化版目录过滤器"""
    
    def __init__(self):
        # 目录关键词列表
        self.toc_keywords = [
            '目录',
            '内容目录', 
            '正文目录', 
            '图表目录',
            '本期目录',
            '图目录',
            '表目录',
            '附录目录',
        ]
        
        # 创建灵活的目录标题匹配模式
        # 匹配：# 任意文字 + 目录关键词（作为结尾）
        # 例如：# 目录、# 内容目录、# 正文目录、# 文章目录、# 报告目录 等
        keywords_pattern = '|'.join(re.escape(keyword) for keyword in self.toc_keywords)
        self.toc_header_pattern = re.compile(f'^#\\s+.*?({keywords_pattern})\\s*$', re.IGNORECASE)
        
        # 目录内容特征
        self.toc_content_patterns = [
            re.compile(r'^\s*\d+\s*$'),  # 纯数字（页码）
            re.compile(r'.*\s+\d+\s*$'),  # 以数字结尾（可能是页码）
            re.compile(r'.*\.{3,}.*\d+\s*$'),  # 包含多个点和数字的行（典型目录格式）
            re.compile(r'^\s*图\d+[：:]\s*'),  # 图1: 图2: 等
            re.compile(r'^\s*表\d+[：:]\s*'),  # 表1: 表2: 等
        ]

        # 目录条目模式：# 开头的标题，但以数字结尾（页码）
        # 例如：# 4.核心优势：治理优秀，上下游壁垒兼备 16
        self.toc_item_pattern = re.compile(r'^#+\s+.*\s+\d+\s*$')
    
    def is_toc_header(self, line: str) -> bool:
        """判断是否是目录标题行"""
        line = line.strip()
        return bool(self.toc_header_pattern.match(line))

    def is_toc_item(self, line: str) -> bool:
        """判断是否是目录条目行（# 开头且以数字结尾）"""
        line = line.strip()
        return bool(self.toc_item_pattern.match(line))

    def is_toc_content(self, line: str) -> bool:
        """判断是否是目录内容行"""
        line = line.strip()
        if not line:
            return True  # 空行也算目录内容的一部分

        for pattern in self.toc_content_patterns:
            if pattern.match(line):
                return True
        return False
    
    def filter_document(self, content: str) -> Tuple[str, int, List[str]]:
        """
        过滤文档中的目录部分

        Args:
            content: 原始文档内容

        Returns:
            (filtered_content, removed_lines_count, removed_sections):
            过滤后的内容、删除的行数、删除的目录标题列表
        """
        lines = content.split('\n')
        filtered_lines = []
        removed_count = 0
        removed_sections = []
        in_toc_section = False
        current_toc_title = ""

        for line in lines:
            # 检查是否是目录标题或目录条目
            if self.is_toc_header(line):
                logger.debug(f"发现目录标题: {line.strip()}")
                in_toc_section = True
                current_toc_title = line.strip()
                removed_sections.append(current_toc_title)
                removed_count += 1
                continue
            elif self.is_toc_item(line):
                logger.debug(f"发现目录条目: {line.strip()}")
                in_toc_section = True
                current_toc_title = line.strip()
                removed_sections.append(current_toc_title)
                removed_count += 1
                continue

            # 如果在目录部分
            if in_toc_section:
                # 检查是否是新的章节开始（以#开头）
                if line.strip().startswith('#'):
                    # 新章节开始，目录部分结束
                    in_toc_section = False
                    filtered_lines.append(line)
                else:
                    # 在目录部分，所有内容都跳过（直到下一个#标题）
                    removed_count += 1
                    continue
            else:
                # 不在目录部分，保留
                filtered_lines.append(line)

        return '\n'.join(filtered_lines), removed_count, removed_sections
    
    def process_file(self, file_path: Path) -> bool:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否成功处理
        """
        try:
            # 读取原文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 过滤目录
            filtered_content, removed_count, removed_sections = self.filter_document(content)
            
            if removed_count > 0:
                # 创建备份文件
                backup_path = file_path.with_suffix('.md.backup')
                if not backup_path.exists():
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.debug(f"创建备份文件: {backup_path}")
                
                # 写入过滤后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(filtered_content)
                
                logger.info(f"✅ {file_path.name}: 删除了 {removed_count} 行，过滤的目录: {', '.join(removed_sections)}")
                return True
            else:
                logger.info(f"⏭️  {file_path.name}: 未发现目录内容")
                return False
                
        except Exception as e:
            logger.error(f"❌ 处理文件 {file_path} 时出错: {e}")
            return False
    
    def process_directory(self, parser_dir: Path) -> Tuple[int, int]:
        """
        处理整个parser目录
        
        Args:
            parser_dir: parser目录路径
            
        Returns:
            (processed_count, total_count): 处理的文件数和总文件数
        """
        processed_count = 0
        total_count = 0
        
        # 遍历所有子目录中的full.md文件
        for subdir in sorted(parser_dir.iterdir()):
            if subdir.is_dir():
                full_md_path = subdir / "full.md"
                if full_md_path.exists():
                    total_count += 1
                    
                    if self.process_file(full_md_path):
                        processed_count += 1
        
        return processed_count, total_count

def test_patterns():
    """测试目录标题匹配模式"""
    filter_obj = SimpleTableOfContentsFilter()
    
    test_lines = [
        # 目录标题测试
        "# 目录",           # ✅ 目录标题
        "# 内容目录",       # ✅ 目录标题
        "# 图表目录",       # ✅ 目录标题
        "# 正文目录",       # ✅ 目录标题
        "# 本期目录",       # ✅ 目录标题
        "# 目录说明",       # ❌ 不匹配（目录不在结尾）

        # 目录条目测试
        "# 4.核心优势：治理优秀，上下游壁垒兼备 16",  # ✅ 目录条目
        "# 二、年内节奏：Q1底部确立，年内逐季加速 13",  # ✅ 目录条目
        "## 1.1 公司概况 5",                        # ✅ 目录条目

        # 正常标题测试
        "# 第一章 概述",    # ❌ 正常标题
        "# 公司简介",       # ❌ 正常标题
        "## 业务分析",      # ❌ 正常标题
    ]
    
    logger.info("测试目录匹配模式:")
    for line in test_lines:
        is_header = filter_obj.is_toc_header(line)
        is_item = filter_obj.is_toc_item(line)

        if is_header:
            status = "✅ 目录标题"
        elif is_item:
            status = "✅ 目录条目"
        else:
            status = "❌ 不匹配"

        logger.info(f"  {line:<50} -> {status}")

def main():
    """主函数"""
    logger.info("开始过滤parser目录下文档中的目录部分")
    
    # 测试匹配模式
    test_patterns()
    logger.info("-" * 50)
    
    # 获取parser目录路径
    current_dir = Path.cwd()
    parser_dir = current_dir / "data" / "parser"
    
    if not parser_dir.exists():
        logger.error(f"Parser目录不存在: {parser_dir}")
        return
    
    # 创建过滤器
    filter_obj = SimpleTableOfContentsFilter()
    
    # 处理目录
    processed_count, total_count = filter_obj.process_directory(parser_dir)
    
    # 输出统计结果
    logger.info("=" * 60)
    logger.info("目录过滤完成")
    logger.info("=" * 60)
    logger.info(f"📁 总文件数: {total_count}")
    logger.info(f"✅ 处理文件数: {processed_count}")
    logger.info(f"⏭️  跳过文件数: {total_count - processed_count}")
    
    if processed_count > 0:
        logger.info("🎉 目录过滤成功完成")
        logger.info("💾 原文件已备份为 .md.backup 格式")
        logger.info("💡 建议重新构建向量索引以获得更好的检索效果")
    else:
        logger.info("ℹ️  所有文件均无需处理")

if __name__ == "__main__":
    main()
