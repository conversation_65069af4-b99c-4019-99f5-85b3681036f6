#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
答案生成模块

功能特性：
1. 使用 Pro/moonshotai/Kimi-K2-Instruct 模型生成答案
2. 基于检索到的文档内容回答问题
3. 支持上下文管理和错误处理

作者: AI Assistant
日期: 2025年
"""

import os
import logging
from typing import List, Dict, Any, Optional
import requests
import json
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

class AnswerGenerator:
    """答案生成器，使用大语言模型生成答案"""
    
    def __init__(self):
        self.setup_llm()
    
    def setup_llm(self):
        """设置大语言模型"""
        self.llm_model = os.getenv('CN_LLM_MODEL', 'Pro/moonshotai/Kimi-K2-Instruct')
        self.base_url = os.getenv('CN_BASE_URL', 'https://api.siliconflow.cn/v1')
        self.api_key = os.getenv('CN_API_KEY')
        self.temperature = float(os.getenv('LLM_TEMPERATURE', '0.1'))
        self.max_tokens = int(os.getenv('LLM_MAX_TOKENS', '10000'))
        
        if not self.api_key:
            raise ValueError("未设置 CN_API_KEY，无法使用答案生成功能")
        
        logger.info(f"答案生成器已初始化，使用模型: {self.llm_model}")
    
    def generate_answer(self, question: str, retrieved_docs: List[Dict[str, Any]],
                       max_context_length: int = 8000) -> str:
        """
        基于检索到的文档生成答案

        Args:
            question: 用户问题
            retrieved_docs: 检索到的文档列表
            max_context_length: 最大上下文长度

        Returns:
            生成的答案
        """
        try:
            # 构建上下文
            context = self._build_context(retrieved_docs, max_context_length)
            
            # 构建提示词
            prompt = self._build_prompt(question, context)
            
            # 调用LLM生成答案
            answer = self._call_llm_api(prompt)
            
            if answer:
                logger.info(f"成功生成答案，长度: {len(answer)} 字符")
                return answer
            else:
                logger.error("LLM返回空答案")
                return self._generate_fallback_answer(question, retrieved_docs)
                
        except Exception as e:
            logger.error(f"答案生成过程中出现错误: {e}")
            return self._generate_fallback_answer(question, retrieved_docs)
    
    def _build_context(self, retrieved_docs: List[Dict[str, Any]], max_length: int) -> str:
        """构建上下文文本"""
        context_parts = []
        current_length = 0
        processed_titles = set()  # 记录已处理的标题，避免重复

        # 只取前3个重排后的结果
        top_docs = retrieved_docs[:3]

        for i, doc in enumerate(top_docs):
            # 获取文档信息
            content = doc.get('content', '')
            metadata = doc.get('metadata', {})
            database = doc.get('database', 'Unknown')
            expanded_context = doc.get('expanded_context', [])

            # 构建文档片段
            doc_info = []

            # 添加来源信息（财报文档特殊处理）
            project_name = metadata.get('project_name', database)
            if project_name:
                # 尝试提取公司名称和报告类型
                if '年报' in project_name:
                    doc_info.append(f"来源: {project_name}（年度报告）")
                elif '季报' in project_name or 'Q1' in project_name or 'Q2' in project_name or 'Q3' in project_name or 'Q4' in project_name:
                    doc_info.append(f"来源: {project_name}（季度报告）")
                elif '研究报告' in project_name or '分析报告' in project_name:
                    doc_info.append(f"来源: {project_name}（研究分析）")
                else:
                    doc_info.append(f"来源: {project_name}")

            # 添加页码信息
            if metadata.get('chunk_type') == 'page':
                page_num = metadata.get('page_number')
                if page_num is not None:
                    doc_info.append(f"页码: 第{page_num}页")

            # 添加标题信息
            title_content = None
            if metadata.get('chunk_type') == 'title':
                title_content = metadata.get('title_content')
                if not title_content:
                    # 尝试从 MarkdownHeaderTextSplitter 元数据获取
                    for j in range(1, 7):
                        header_key = f"Header {j}"
                        if header_key in metadata:
                            title_content = metadata[header_key]
                            break

                if title_content:
                    doc_info.append(f"章节: {title_content}")

            # 添加重排序分数（如果有）
            rerank_score = doc.get('rerank_score')
            if rerank_score is not None:
                doc_info.append(f"相关性: {rerank_score:.3f}")

            # 检查是否已处理过相同标题
            title_key = f"{database}_{title_content}" if title_content else f"{database}_{i}"
            if title_key in processed_titles:
                logger.debug(f"跳过重复标题: {title_content}")
                continue

            # 构建完整的文档内容（包含扩展上下文）
            full_content = content

            # 如果有扩展上下文，合并内容
            if expanded_context:
                expanded_contents = [exp_doc.get('content', '') for exp_doc in expanded_context]
                if expanded_contents:
                    full_content = content + "\n\n" + "\n\n".join(expanded_contents)
                    doc_info.append(f"扩展内容: {len(expanded_context)}个相关块")

            # 构建完整的文档片段
            doc_header = f"【文档 {i+1}】" + ("（" + "，".join(doc_info) + "）" if doc_info else "")
            doc_text = f"{doc_header}\n{full_content}\n"

            # 检查长度限制
            if current_length + len(doc_text) > max_length:
                if current_length == 0:  # 如果第一个文档就超长，截断它
                    available_length = max_length - len(doc_header) - 10
                    truncated_content = full_content[:available_length] + "..."
                    doc_text = f"{doc_header}\n{truncated_content}\n"
                    context_parts.append(doc_text)
                break

            context_parts.append(doc_text)
            current_length += len(doc_text)
            processed_titles.add(title_key)  # 标记已处理

        logger.info(f"构建上下文完成，使用了 {len(context_parts)} 个文档，总长度: {current_length} 字符")
        return "\n".join(context_parts)
    
    def _build_prompt(self, question: str, context: str) -> str:
        """构建提示词"""
        system_prompt = """你是一个专业的财务报告和企业年报分析专家，擅长从财报、年报、研究报告等企业文档中提取和分析信息。

请遵循以下原则：
1. 仅基于提供的文档内容回答问题，不要添加文档中没有的信息
2. 对于财务数据、业绩指标、经营状况等信息，要准确引用原文中的具体数字和表述
3. 如果涉及时间范围，请明确指出数据对应的年份、季度或期间
4. 对于趋势分析、同比增长等信息，要基于文档中的具体数据进行说明
5. 如果文档中没有足够信息回答问题，请明确说明缺少哪些关键信息
6. 回答要结构化、有条理，对于复杂问题可以分点回答
7. 保持客观专业的语调，避免主观判断
8. 对于技术术语、行业专业词汇要准确使用
9. 如果涉及多个公司或项目的对比，要清楚区分各自的情况"""

        user_prompt = f"""基于以下财报/年报/研究报告文档内容，请专业地回答问题：

问题：{question}

相关文档内容：
{context}

请基于上述文档内容，以专业财务分析师的角度回答问题。如果涉及具体数据，请准确引用；如果涉及分析判断，请基于文档中的事实依据："""

        return f"系统提示：{system_prompt}\n\n用户提示：{user_prompt}"
    
    def _call_llm_api(self, prompt: str, max_retries: int = 3) -> Optional[str]:
        """调用LLM API"""
        url = f"{self.base_url}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.llm_model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "stream": False
        }
        
        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=headers, json=data, timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if 'choices' in result and len(result['choices']) > 0:
                        answer = result['choices'][0]['message']['content'].strip()
                        return answer
                    else:
                        logger.error(f"LLM API返回格式错误: {result}")
                        return None
                        
                elif response.status_code == 429:
                    # 速率限制，等待后重试
                    wait_time = 2 ** attempt
                    logger.warning(f"API速率限制，等待 {wait_time} 秒后重试")
                    time.sleep(wait_time)
                    continue
                    
                else:
                    # HTTP错误状态码也需要重试
                    logger.error(f"LLM API调用失败: {response.status_code}, {response.text}")
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # 指数退避: 1s, 2s, 4s
                        logger.info(f"等待{wait_time}秒后进行第 {attempt + 2} 次重试...")
                        time.sleep(wait_time)
                        continue  # 继续重试
                    return None
                    
            except requests.exceptions.Timeout:
                logger.warning(f"LLM API调用超时，尝试 {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                    
            except Exception as e:
                logger.error(f"LLM API调用异常: {e}")
                return None
        
        logger.error("LLM API调用失败，已达到最大重试次数")
        return None
    
    def _generate_fallback_answer(self, question: str, retrieved_docs: List[Dict[str, Any]]) -> str:
        """生成备用答案"""
        if not retrieved_docs:
            return "抱歉，在现有的财报和研究报告文档中没有找到与该问题相关的内容。建议检查问题中的公司名称、时间范围或具体指标是否准确。"

        # 基于最相关文档生成结构化的备用答案
        top_doc = retrieved_docs[0]
        content = top_doc.get('content', '')
        metadata = top_doc.get('metadata', {})

        # 获取文档来源信息
        source_info = []
        project_name = metadata.get('project_name', '未知文档')
        if project_name:
            source_info.append(f"来源：{project_name}")

        if metadata.get('chunk_type') == 'page':
            page_num = metadata.get('page_number')
            if page_num:
                source_info.append(f"页码：{page_num}")

        source_text = "（" + "，".join(source_info) + "）" if source_info else ""

        # 截取内容
        if len(content) > 300:
            content = content[:300] + "..."

        return f"""基于检索到的相关文档内容{source_text}：

{content}

注：由于AI模型暂时无法生成完整回答，以上为检索到的最相关文档片段。建议根据具体问题"{question}"进一步查阅完整的财报或研究报告文档。"""

    def generate_complete_answer(self, question: str, retrieved_docs: List[Dict[str, Any]],
                                max_context_length: int = 8000) -> Dict[str, Any]:
        """
        生成完整的答案结果，包含filename、page、question、answer

        Args:
            question: 用户问题
            retrieved_docs: 检索到的文档列表
            max_context_length: 最大上下文长度

        Returns:
            包含filename、page、question、answer的字典
        """
        # 生成答案
        answer = self.generate_answer(question, retrieved_docs, max_context_length)

        # 提取filename和page信息
        filename, page = self._extract_file_info(retrieved_docs)

        return {
            "filename": filename,
            "page": page,
            "question": question,
            "answer": answer
        }

    def _extract_file_info(self, retrieved_docs: List[Dict[str, Any]]) -> tuple[str, int]:
        """
        从检索结果中提取filename和page信息

        Args:
            retrieved_docs: 检索到的文档列表或新格式的页面对象列表

        Returns:
            (filename, page) 元组
        """
        if not retrieved_docs:
            return "xx.pdf", 1

        first_doc = retrieved_docs[0]

        # 新格式：直接从页面对象中提取信息
        if 'page_number' in first_doc and 'project_name' in first_doc:
            page_number = first_doc.get('page_number', 1)
            project_name = first_doc.get('project_name', '')
            filename = self._format_filename(project_name)
            
            logger.debug(f"从新格式页面对象提取: {filename}, 页码: {page_number}")
            return filename, page_number

        # 优先检查标题扩展检索的目标页码
        target_page = first_doc.get('target_page_number')
        if target_page:
            logger.info(f"使用标题扩展检索找到的页码: {target_page}")

            # 从原始数据中提取项目名称
            original_data = first_doc.get('original_data', {})
            if original_data:
                original_chunk = original_data.get('original_chunk')
                if original_chunk and hasattr(original_chunk, 'metadata'):
                    project_name = original_chunk.metadata.get('project_name', '')
                    if project_name:
                        filename = self._format_filename(project_name)
                        logger.debug(f"从标题扩展检索提取: {filename}, 页码: {target_page}")
                        return filename, target_page

        # 其次从页码分块中提取信息
        for doc in retrieved_docs:
            metadata = doc.get('metadata', {})

            # 如果是页码分块，直接提取页码和项目名
            if metadata.get('chunk_type') == 'page':
                page_number = metadata.get('page_number', 1)
                project_name = self._get_project_name_from_doc(doc)
                filename = self._format_filename(project_name)

                logger.debug(f"从页码分块提取: {filename}, 页码: {page_number}")
                return filename, page_number

        # 如果没有页码分块，从第一个文档提取项目名，页码默认为1
        project_name = self._get_project_name_from_doc(first_doc)
        filename = self._format_filename(project_name)

        logger.debug(f"从首个文档提取: {filename}, 页码: 1")
        return filename, 1

    def _get_project_name_from_doc(self, doc: Dict[str, Any]) -> str:
        """从文档中获取项目名称"""
        # 首先尝试从文档的metadata中获取
        metadata = doc.get('metadata', {})
        project_name = metadata.get('project_name')

        if project_name:
            return project_name

        # 如果没有，尝试从数据库名称通过映射获取
        database = doc.get('database', '')
        if database:
            # 尝试加载数据库映射
            try:
                import json
                from pathlib import Path

                # 获取数据库映射文件路径
                current_dir = Path.cwd()
                mapping_file = current_dir / "data" / "db" / "db_mapping.json"

                if mapping_file.exists():
                    with open(mapping_file, 'r', encoding='utf-8') as f:
                        db_mapping = json.load(f)

                    project_name = db_mapping.get(database)
                    if project_name:
                        return project_name

            except Exception as e:
                logger.warning(f"加载数据库映射失败: {e}")

            # 如果映射失败，返回数据库名作为备选
            return database

        return "未知项目"

    def _format_filename(self, project_name: str) -> str:
        """格式化文件名，确保有.pdf后缀"""
        if not project_name or project_name == "未知项目":
            return "xx.pdf"

        # 如果已经有.pdf后缀，直接返回
        if project_name.endswith('.pdf'):
            return project_name

        # 添加.pdf后缀
        return f"{project_name}.pdf"

def main():
    """测试答案生成功能"""
    # 创建答案生成器
    try:
        generator = AnswerGenerator()
    except ValueError as e:
        print(f"初始化失败: {e}")
        return
    
    # 测试数据
    question = "广联达的BIM技术有哪些应用？"
    retrieved_docs = [
        {
            "content": "广联达是中国领先的数字建筑平台服务商，专注于BIM技术的研发和应用。公司的BIM技术主要应用于建筑设计、施工管理和项目协同等领域。",
            "metadata": {
                "project_name": "广联达-数字建筑报告",
                "chunk_type": "page",
                "page_number": 5
            },
            "database": "project_test",
            "rerank_score": 0.95
        },
        {
            "content": "通过BIM技术，广联达帮助建筑企业实现数字化转型，提高设计效率和施工质量。",
            "metadata": {
                "project_name": "广联达-数字建筑报告",
                "chunk_type": "title",
                "title_content": "BIM技术应用"
            },
            "database": "project_test",
            "rerank_score": 0.88
        }
    ]
    
    print(f"问题: {question}")
    print(f"检索到的文档数量: {len(retrieved_docs)}")
    
    # 生成答案
    answer = generator.generate_answer(question, retrieved_docs)
    
    print(f"\n生成的答案:\n{answer}")

if __name__ == "__main__":
    main()
