#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试匹配分数 - 分析为什么期望项目的排名这么低
"""

import os
import logging
from pathlib import Path
from dotenv import load_dotenv

# 导入数据库匹配器
from modules.vector_retriever import DatabaseMatcher

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_guanglianda_matching():
    """调试广联达"三条增长曲线"项目的匹配分数"""
    
    print("=" * 80)
    print("广联达「三条增长曲线」项目匹配分数分析")
    print("=" * 80)
    
    # 初始化匹配器
    db_dir = Path("data/db")
    matcher = DatabaseMatcher(db_dir)
    
    # 目标查询
    query = "如何分析广联达在2006年至2020年间营业收入和净利润的增长趋势？"
    expected_keyword = "三条增长曲线"
    
    print(f"🔍 查询: {query[:50]}...")
    print(f"🎯 期望关键词: {expected_keyword}")
    
    # 获取所有广联达项目及其分数
    bm25_scores = matcher.get_bm25_scores(query)
    semantic_scores = matcher.get_semantic_scores(query)
    
    # 找到所有广联达项目
    guanglianda_projects = []
    for i, (project_key, info) in enumerate(matcher.company_representatives.items()):
        if "广联达" in info['company_name']:
            guanglianda_projects.append({
                'index': i,
                'project_name': info['project_name'],
                'db_name': info['db_name'],
                'bm25_score': bm25_scores[i] if i < len(bm25_scores) else 0,
                'semantic_score': semantic_scores[i] if i < len(semantic_scores) else 0,
                'is_target': expected_keyword in info['project_name']
            })
    
    # 计算混合分数
    bm25_max = max(bm25_scores) if max(bm25_scores) > 0 else 1
    semantic_max = max(semantic_scores) if max(semantic_scores) > 0 else 1
    
    for proj in guanglianda_projects:
        proj['bm25_norm'] = proj['bm25_score'] / bm25_max
        proj['semantic_norm'] = proj['semantic_score'] / semantic_max
        proj['hybrid_score'] = 0.7 * proj['bm25_norm'] + 0.3 * proj['semantic_norm']
    
    # 按混合分数排序
    guanglianda_projects.sort(key=lambda x: x['hybrid_score'], reverse=True)
    
    print(f"\n📊 广联达项目分数排序 (共{len(guanglianda_projects)}个):")
    target_rank = None
    
    for rank, proj in enumerate(guanglianda_projects):
        status = "🎯" if proj['is_target'] else "  "
        if proj['is_target']:
            target_rank = rank + 1
            
        print(f"{rank+1:2d}. {status} {proj['project_name'][:60]}...")
        print(f"       BM25: {proj['bm25_score']:.3f} (归一: {proj['bm25_norm']:.3f}) | "
              f"语义: {proj['semantic_score']:.3f} (归一: {proj['semantic_norm']:.3f}) | "
              f"混合: {proj['hybrid_score']:.3f}")
        print()
    
    if target_rank:
        print(f"🎯 目标项目排名: 第{target_rank}位 / {len(guanglianda_projects)}个广联达项目")
    else:
        print(f"❌ 未找到目标项目！")

def debug_lingyun_disappearance():
    """调试凌云股份项目为什么消失了"""
    
    print("\n" + "=" * 80)
    print("凌云股份项目消失原因分析")
    print("=" * 80)
    
    # 初始化匹配器
    db_dir = Path("data/db")
    matcher = DatabaseMatcher(db_dir)
    
    # 凌云股份查询
    query = "根据凌云股份（600480）的深度研究报告，请问亚大集团在乘用车和商用车管路产品应用方面分别有哪些具体的产品？"
    
    print(f"🔍 查询: {query[:50]}...")
    
    # 获取匹配结果
    matches = matcher.match_databases(query, max_matches=20)
    
    # 找到凌云股份项目
    lingyun_found = False
    for rank, (db_name, score) in enumerate(matches):
        project_name = "未知项目"
        for info in matcher.company_representatives.values():
            if info['db_name'] == db_name:
                project_name = info['project_name']
                break
        
        if "凌云股份" in project_name:
            lingyun_found = True
            print(f"✅ 凌云股份项目找到: 排名第{rank+1}位")
            print(f"   项目: {project_name}")
            print(f"   分数: {score:.3f}")
            break
    
    if not lingyun_found:
        print(f"❌ 凌云股份项目未在TOP20中找到！")
        
        # 检查凌云股份项目是否存在
        lingyun_projects = []
        for info in matcher.company_representatives.values():
            if "凌云股份" in info['company_name'] or "凌云股份" in info['project_name']:
                lingyun_projects.append(info)
        
        print(f"\n📋 凌云股份项目列表 (共{len(lingyun_projects)}个):")
        for i, proj in enumerate(lingyun_projects):
            print(f"   {i+1}. {proj['project_name']}")
        
        # 分析凌云股份项目的具体分数
        if lingyun_projects:
            lingyun_info = lingyun_projects[0]  # 取第一个
            
            # 找到该项目的索引
            lingyun_index = None
            for i, (project_key, info) in enumerate(matcher.company_representatives.items()):
                if info['db_name'] == lingyun_info['db_name']:
                    lingyun_index = i
                    break
            
            if lingyun_index is not None:
                bm25_scores = matcher.get_bm25_scores(query)
                semantic_scores = matcher.get_semantic_scores(query)
                
                lingyun_bm25 = bm25_scores[lingyun_index] if lingyun_index < len(bm25_scores) else 0
                lingyun_semantic = semantic_scores[lingyun_index] if lingyun_index < len(semantic_scores) else 0
                
                bm25_max = max(bm25_scores) if max(bm25_scores) > 0 else 1
                semantic_max = max(semantic_scores) if max(semantic_scores) > 0 else 1
                
                lingyun_bm25_norm = lingyun_bm25 / bm25_max
                lingyun_semantic_norm = lingyun_semantic / semantic_max
                lingyun_hybrid = 0.7 * lingyun_bm25_norm + 0.3 * lingyun_semantic_norm
                
                print(f"\n📊 凌云股份项目分数:")
                print(f"   BM25: {lingyun_bm25:.3f} (归一: {lingyun_bm25_norm:.3f})")
                print(f"   语义: {lingyun_semantic:.3f} (归一: {lingyun_semantic_norm:.3f})")
                print(f"   混合: {lingyun_hybrid:.3f}")
                
                # 查看TOP5项目的分数
                print(f"\n📋 TOP5项目分数对比:")
                for rank, (db_name, score) in enumerate(matches[:5]):
                    project_name = "未知项目"
                    for info in matcher.company_representatives.values():
                        if info['db_name'] == db_name:
                            project_name = info['project_name'][:50] + "..."
                            break
                    print(f"   {rank+1}. {project_name} (分数: {score:.3f})")

def check_bm25_content():
    """检查BM25文档内容是否正确"""
    
    print("\n" + "=" * 80)
    print("BM25文档内容检查")
    print("=" * 80)
    
    # 初始化匹配器
    db_dir = Path("data/db")
    matcher = DatabaseMatcher(db_dir)
    
    # 检查几个关键项目的BM25内容
    target_projects = [
        "广联达-再谈广联达当前时点下如何看待其三条增长曲线",
        "凌云股份-公司深度研究报告热成型电池盒双轮驱动传感器加速布局",
        "伊利股份-公司深度报告王者荣耀行稳致远"
    ]
    
    print("🔍 关键项目的BM25文档内容:")
    
    # 获取BM25检索器的文档
    if matcher.bm25_retriever:
        bm25_docs = matcher.bm25_retriever.docs
        
        for target in target_projects:
            print(f"\n📄 查找项目: {target[:50]}...")
            found = False
            
            for doc in bm25_docs:
                project_name = doc.metadata.get('project_name', '')
                if target in project_name:
                    found = True
                    print(f"   ✅ 找到匹配项目: {project_name[:60]}...")
                    print(f"   📝 BM25内容: {doc.page_content[:100]}...")
                    print(f"   🏷️ 公司变体: {doc.metadata.get('company_variants', [])}")
                    print(f"   🔑 项目关键词: {doc.metadata.get('project_keywords', [])}")
                    break
            
            if not found:
                print(f"   ❌ 未找到匹配项目")

if __name__ == "__main__":
    debug_guanglianda_matching()
    debug_lingyun_disappearance()
    check_bm25_content()