#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG比赛项目主程序

功能特性：
1. 读取测试数据文件
2. 执行完整的RAG流程：检索 → 重排序 → 答案生成
3. 输出符合比赛格式的答案文件

作者: AI Assistant
日期: 2025年
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any
import time
from dotenv import load_dotenv
import multiprocessing as mp
import queue
import threading
from concurrent.futures import ProcessPoolExecutor, as_completed
import os

# 导入自定义模块
from modules.vector_retriever import VectorRetriever
from modules.answer_generator import AnswerGenerator

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rag_main.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def worker_process(worker_id: int, task_queue: mp.Queue, result_queue: mp.Queue,
                  db_dir: Path):
    """
    工作进程函数，处理分配的任务

    Args:
        worker_id: 工作进程ID
        task_queue: 任务队列
        result_queue: 结果队列
        db_dir: 向量数据库目录
    """
    # 在子进程中初始化组件
    try:
        # 设置进程名称用于日志
        process_name = f"Worker-{worker_id}"

        # 初始化RAG组件
        retriever = VectorRetriever(db_dir)
        generator = AnswerGenerator()

        logger.info(f"{process_name}: 工作进程启动成功")

        while True:
            try:
                # 从任务队列获取任务，超时5秒
                task = task_queue.get(timeout=5)

                # 检查是否是结束信号
                if task is None:
                    logger.info(f"{process_name}: 收到结束信号，退出")
                    break

                task_index, question_data = task
                question = question_data.get("question", "")
                filename = question_data.get("filename", "xx.pdf")
                page = question_data.get("page", 1)

                logger.info(f"{process_name}: 处理任务 {task_index + 1}: {question[:50]}...")

                # 处理问题
                result = process_single_question(
                    retriever, generator, question, filename, page,
                    max_retries=3, process_name=process_name
                )

                # 将结果放入结果队列
                result_queue.put((task_index, result))
                logger.info(f"{process_name}: 任务 {task_index + 1} 完成")

            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"{process_name}: 处理任务时出错: {e}")
                # 即使出错也要返回结果
                if 'task_index' in locals():
                    error_result = {
                        "filename": filename if 'filename' in locals() else "xx.pdf",
                        "page": page if 'page' in locals() else 1,
                        "question": question if 'question' in locals() else "",
                        "answer": f"处理过程中出现错误：{str(e)}"
                    }
                    result_queue.put((task_index, error_result))

    except Exception as e:
        logger.error(f"Worker-{worker_id}: 初始化失败: {e}")

def process_single_question(retriever, generator, question: str, filename: str, page: int,
                          max_retries: int = 3, process_name: str = "Worker") -> Dict[str, Any]:
    """
    处理单个问题（多进程版本）

    Args:
        retriever: 检索器实例
        generator: 生成器实例
        question: 问题文本
        filename: 文件名
        page: 页码
        max_retries: 最大重试次数
        process_name: 进程名称（用于日志）

    Returns:
        包含问题和答案的字典
    """
    # 确保filename包含.pdf扩展名
    if not filename.endswith('.pdf'):
        filename = filename + '.pdf' if filename != 'xx' else 'xx.pdf'

    last_error = None

    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                wait_time = 2 ** (attempt - 1)  # 指数退避：1s, 2s, 4s
                logger.info(f"{process_name}: 第 {attempt + 1} 次尝试，等待 {wait_time} 秒...")
                time.sleep(wait_time)

            # 执行检索（页面级别检索）
            start_time = time.time()
            retrieved_page = retriever.retrieve(query=question)
            retrieval_time = time.time() - start_time
            
            if retrieved_page:
                logger.info(f"{process_name}: 检索完成，耗时: {retrieval_time:.2f}秒，找到最佳页面: {retrieved_page.get('project_name', 'N/A')} 第{retrieved_page.get('page_number', 'N/A')}页")
            else:
                logger.warning(f"{process_name}: 检索完成，耗时: {retrieval_time:.2f}秒，未找到相关页面")

            # 生成答案
            start_time = time.time()
            result = generator.generate_complete_answer(question, [retrieved_page] if retrieved_page else [])
            generation_time = time.time() - start_time
            logger.info(f"{process_name}: 答案生成完成，耗时: {generation_time:.2f}秒")

            # 使用RAG系统推理得到的结果，不再强制覆盖
            # result中已经包含了正确的filename和page

            if attempt > 0:
                logger.info(f"{process_name}: 重试成功！第 {attempt + 1} 次尝试成功")

            return result

        except Exception as e:
            last_error = e
            error_msg = str(e)

            # 判断是否应该重试
            should_retry = _should_retry_error(error_msg)

            if attempt < max_retries and should_retry:
                logger.warning(f"{process_name}: 第 {attempt + 1} 次尝试失败: {error_msg}，将重试...")
            else:
                if attempt >= max_retries:
                    logger.error(f"{process_name}: 达到最大重试次数 ({max_retries})，处理失败: {error_msg}")
                else:
                    logger.error(f"{process_name}: 不可重试的错误: {error_msg}")
                break

    # 所有重试都失败了
    return {
        "filename": filename,
        "page": page,
        "question": question,
        "answer": f"抱歉，处理这个问题时出现了错误：{str(last_error)}"
    }

def _should_retry_error(error_msg: str) -> bool:
    """判断错误是否应该重试（独立函数版本）"""
    retry_keywords = [
        "429",  # 速率限制
        "timeout",  # 超时
        "connection",  # 连接错误
        "network",  # 网络错误
        "502", "503", "504",  # 服务器错误
        "internal server error",  # 内部服务器错误
        "service unavailable",  # 服务不可用
        "bad gateway",  # 网关错误
    ]

    error_lower = error_msg.lower()
    return any(keyword in error_lower for keyword in retry_keywords)

class RAGPipeline:
    """RAG流水线，协调检索和生成过程"""

    def __init__(self, db_dir: Path):
        """
        初始化RAG流水线

        Args:
            db_dir: 向量数据库目录
        """
        self.db_dir = db_dir

        # 创建中间结果保存目录
        self.progress_dir = Path.cwd() / "progress"
        self.progress_dir.mkdir(exist_ok=True)

        # 初始化组件
        logger.info("初始化RAG流水线...")
        self.retriever = VectorRetriever(db_dir)
        self.generator = AnswerGenerator()
        logger.info("RAG流水线初始化完成")

    def process_question(self, question: str, filename: str = "xx.pdf", page: int = 1,
                        max_retries: int = 3) -> Dict[str, Any]:
        """
        处理单个问题（带重试机制）

        Args:
            question: 问题文本
            filename: 文件名（用于输出格式）
            page: 页码（用于输出格式）
            max_retries: 最大重试次数

        Returns:
            包含问题和答案的字典
        """
        logger.info(f"处理问题: {question[:50]}...")

        # 确保filename包含.pdf扩展名
        if not filename.endswith('.pdf'):
            filename = filename + '.pdf' if filename != 'xx' else 'xx.pdf'

        last_error = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    wait_time = 2 ** (attempt - 1)  # 指数退避：1s, 2s, 4s
                    logger.info(f"第 {attempt + 1} 次尝试，等待 {wait_time} 秒...")
                    time.sleep(wait_time)

                # 执行检索（页面级别检索）
                start_time = time.time()
                retrieved_page = self.retriever.retrieve(query=question)
                retrieval_time = time.time() - start_time
                
                if retrieved_page:
                    logger.info(f"检索完成，耗时: {retrieval_time:.2f}秒，找到最佳页面: {retrieved_page.get('project_name', 'N/A')} 第{retrieved_page.get('page_number', 'N/A')}页")
                else:
                    logger.warning(f"检索完成，耗时: {retrieval_time:.2f}秒，未找到相关页面")

                # 生成完整答案（包含从检索结果提取的filename和page）
                start_time = time.time()
                result = self.generator.generate_complete_answer(question, [retrieved_page] if retrieved_page else [])
                generation_time = time.time() - start_time
                logger.info(f"答案生成完成，耗时: {generation_time:.2f}秒")

                # 使用RAG系统推理得到的结果，不再强制覆盖
                # result中已经包含了正确的filename和page

                # 成功处理，返回结果
                if attempt > 0:
                    logger.info(f"重试成功！第 {attempt + 1} 次尝试成功")

                return result

            except Exception as e:
                last_error = e
                error_msg = str(e)

                # 判断是否应该重试
                should_retry = self._should_retry_error(error_msg)

                if attempt < max_retries and should_retry:
                    logger.warning(f"第 {attempt + 1} 次尝试失败: {error_msg}，将重试...")
                else:
                    if attempt >= max_retries:
                        logger.error(f"达到最大重试次数 ({max_retries})，处理失败: {error_msg}")
                    else:
                        logger.error(f"不可重试的错误: {error_msg}")
                    break

        # 所有重试都失败了
        return {
            "filename": filename,
            "page": page,
            "question": question,
            "answer": f"抱歉，处理这个问题时出现了错误：{str(last_error)}"
        }

    def _should_retry_error(self, error_msg: str) -> bool:
        """判断错误是否应该重试"""
        retry_keywords = [
            "429",  # 速率限制
            "timeout",  # 超时
            "connection",  # 连接错误
            "network",  # 网络错误
            "502", "503", "504",  # 服务器错误
            "internal server error",  # 内部服务器错误
            "service unavailable",  # 服务不可用
            "bad gateway",  # 网关错误
        ]

        error_lower = error_msg.lower()
        return any(keyword in error_lower for keyword in retry_keywords)

    def process_batch_multiprocess(self, questions: List[Dict[str, Any]],
                                  output_file: str = "submit.json",
                                  resume_from: int = 0,
                                  num_processes: int = 4) -> List[Dict[str, Any]]:
        """
        多进程批量处理问题（支持中断继续）

        Args:
            questions: 问题列表，每个问题包含 filename, page, question 字段
            output_file: 输出文件路径
            resume_from: 从第几个问题开始处理（0表示从头开始）
            num_processes: 并发进程数量

        Returns:
            处理结果列表
        """
        # 检查是否有中间结果可以恢复
        results, start_index = self._load_progress(questions, resume_from)

        if start_index > 0:
            logger.info(f"从第 {start_index + 1} 个问题继续处理（已完成 {start_index} 个）")
        else:
            logger.info(f"开始多进程批量处理 {len(questions)} 个问题，使用 {num_processes} 个进程")

        # 准备待处理的问题
        remaining_questions = questions[start_index:]
        if not remaining_questions:
            logger.info("所有问题已处理完成")
            return results

        total_start_time = time.time()

        # 创建进程间通信队列
        task_queue = mp.Queue()
        result_queue = mp.Queue()

        # 将任务放入队列
        for i, question in enumerate(remaining_questions):
            task_queue.put((start_index + i, question))

        # 启动工作进程
        processes = []
        for worker_id in range(num_processes):
            p = mp.Process(
                target=worker_process,
                args=(worker_id, task_queue, result_queue, self.db_dir)
            )
            p.start()
            processes.append(p)

        logger.info(f"已启动 {num_processes} 个工作进程")

        # 收集结果
        completed_count = 0
        total_tasks = len(remaining_questions)

        try:
            while completed_count < total_tasks:
                try:
                    # 从结果队列获取结果，超时30秒
                    task_index, result = result_queue.get(timeout=30)

                    # 确保结果列表足够长
                    while len(results) <= task_index:
                        results.append(None)

                    results[task_index] = result
                    completed_count += 1

                    current_progress = start_index + completed_count
                    logger.info(f"进度: {current_progress}/{len(questions)} ({completed_count}/{total_tasks} 本轮)")

                    # 每完成10个任务保存一次中间结果
                    if current_progress % 10 == 0:
                        filename = self.progress_dir / f"intermediate_{current_progress}.json"
                        self._save_intermediate_results(results, str(filename))
                        logger.info(f"已保存中间结果到 {filename}")

                except queue.Empty:
                    logger.warning("等待结果超时，检查进程状态...")
                    # 检查进程是否还活着
                    alive_processes = [p for p in processes if p.is_alive()]
                    if not alive_processes:
                        logger.error("所有工作进程都已退出")
                        break
                    continue

        except KeyboardInterrupt:
            logger.info("\n检测到中断信号，正在停止工作进程...")

        finally:
            # 发送结束信号给所有进程
            for _ in range(num_processes):
                task_queue.put(None)

            # 等待所有进程结束
            for p in processes:
                p.join(timeout=10)
                if p.is_alive():
                    logger.warning(f"强制终止进程 {p.pid}")
                    p.terminate()
                    p.join()

        # 过滤掉None结果
        results = [r for r in results if r is not None]

        total_time = time.time() - total_start_time
        processed_count = completed_count
        if processed_count > 0:
            logger.info(f"多进程处理完成，总耗时: {total_time:.2f}秒，平均每题: {total_time/processed_count:.2f}秒")

        # 保存最终结果
        self._save_results(results, output_file)

        return results

    def process_batch(self, questions: List[Dict[str, Any]],
                     output_file: str = "submit.json",
                     resume_from: int = 0) -> List[Dict[str, Any]]:
        """
        批量处理问题（支持中断继续）

        Args:
            questions: 问题列表，每个问题包含 filename, page, question 字段
            output_file: 输出文件路径
            resume_from: 从第几个问题开始处理（0表示从头开始）

        Returns:
            处理结果列表
        """
        # 检查是否有中间结果可以恢复
        results, start_index = self._load_progress(questions, resume_from)

        if start_index > 0:
            logger.info(f"从第 {start_index + 1} 个问题继续处理（已完成 {start_index} 个）")
        else:
            logger.info(f"开始批量处理 {len(questions)} 个问题")

        total_start_time = time.time()

        try:
            for i in range(start_index, len(questions)):
                q = questions[i]
                current_num = i + 1
                logger.info(f"处理进度: {current_num}/{len(questions)}")

                result = self.process_question(
                    question=q.get("question", ""),
                    filename=q.get("filename", "xx.pdf"),
                    page=q.get("page", 1)
                )

                results.append(result)

                # 每处理10个问题保存一次中间结果
                if current_num % 10 == 0:
                    filename = self.progress_dir / f"intermediate_{current_num}.json"
                    self._save_intermediate_results(results, str(filename))
                    logger.info(f"已保存中间结果到 {filename}")

        except KeyboardInterrupt:
            logger.info("\n检测到中断信号，正在保存当前进度...")
            current_progress = len(results)
            filename = self.progress_dir / f"interrupted_at_{current_progress}.json"
            self._save_intermediate_results(results, str(filename))
            logger.info(f"进度已保存到 {filename}")
            logger.info(f"要继续处理，请重新运行: python main.py")
            raise

        except Exception as e:
            logger.error(f"批量处理过程中出现错误: {e}")
            current_progress = len(results)
            filename = self.progress_dir / f"error_at_{current_progress}.json"
            self._save_intermediate_results(results, str(filename))
            logger.info(f"错误前的进度已保存到 {filename}")
            raise

        total_time = time.time() - total_start_time
        processed_count = len(results) - start_index
        logger.info(f"批量处理完成，总耗时: {total_time:.2f}秒，平均每题: {total_time/processed_count:.2f}秒")

        # 保存最终结果
        self._save_results(results, output_file)

        return results

    def _load_progress(self, questions: List[Dict[str, Any]],
                      resume_from: int) -> tuple[List[Dict[str, Any]], int]:
        """
        加载处理进度

        Args:
            questions: 问题列表
            resume_from: 指定的恢复位置

        Returns:
            (已处理的结果列表, 开始处理的索引)
        """
        # 如果指定了恢复位置
        if resume_from > 0:
            # 查找对应的中间结果文件
            intermediate_files = [
                self.progress_dir / f"intermediate_{resume_from}.json",
                self.progress_dir / f"interrupted_at_{resume_from}.json",
                self.progress_dir / f"error_at_{resume_from}.json"
            ]

            for filepath in intermediate_files:
                if filepath.exists():
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            results = json.load(f)
                        logger.info(f"从 {filepath} 恢复进度，已完成 {len(results)} 个问题")
                        return results, len(results)
                    except Exception as e:
                        logger.warning(f"加载进度文件 {filepath} 失败: {e}")

            logger.warning(f"未找到位置 {resume_from} 的进度文件，从头开始")
            return [], 0

        # 自动查找最新的中间结果文件
        intermediate_files = []

        # 确保进度目录存在
        if not self.progress_dir.exists():
            logger.info("进度目录不存在，从头开始处理")
            return [], 0

        # 查找所有中间结果文件
        for pattern in ["intermediate_*.json", "interrupted_at_*.json", "error_at_*.json"]:
            intermediate_files.extend(self.progress_dir.glob(pattern))

        if not intermediate_files:
            logger.info("未找到中间结果文件，从头开始处理")
            return [], 0

        # 找到最新的文件（按修改时间）
        latest_file = max(intermediate_files, key=lambda f: f.stat().st_mtime)

        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            # 验证结果的有效性
            if len(results) > len(questions):
                logger.warning(f"中间结果文件 {latest_file} 的问题数量超过输入，从头开始")
                return [], 0

            logger.info(f"自动从 {latest_file} 恢复进度，已完成 {len(results)} 个问题")
            return results, len(results)

        except Exception as e:
            logger.warning(f"加载最新进度文件 {latest_file} 失败: {e}，从头开始")
            return [], 0

    def _save_intermediate_results(self, results: List[Dict[str, Any]], filename: str):
        """保存中间结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"中间结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")

    def _save_results(self, results: List[Dict[str, Any]], filename: str):
        """保存最终结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")

def load_test_data(file_path: str) -> List[Dict[str, Any]]:
    """加载测试数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"成功加载 {len(data)} 个测试问题")
        return data
    except Exception as e:
        logger.error(f"加载测试数据失败: {e}")
        return []

def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='RAG比赛项目')
    parser.add_argument('--multiprocess', action='store_true',
                       help='启用多进程处理（默认：单进程）')
    parser.add_argument('--num-processes', type=int, default=4,
                       help='多进程数量（默认：4）')
    parser.add_argument('--resume-from', type=int, default=0,
                       help='从第几个问题开始处理（默认：0，自动检测）')

    args = parser.parse_args()

    logger.info("=" * 60)
    logger.info("RAG比赛项目启动")
    if args.multiprocess:
        logger.info(f"模式: 多进程处理 ({args.num_processes} 个进程)")
    else:
        logger.info("模式: 单进程处理")
    logger.info("=" * 60)

    # 配置路径
    current_dir = Path.cwd()
    db_dir = current_dir / "data" / "db"
    test_file = current_dir / "data" / "test.json"
    output_file = current_dir / "submit.json"

    # 检查必要文件
    if not db_dir.exists():
        logger.error(f"向量数据库目录不存在: {db_dir}")
        logger.error("请先运行 build_vector_index.py 构建向量索引")
        return

    if not test_file.exists():
        logger.error(f"测试数据文件不存在: {test_file}")
        return

    try:
        # 加载测试数据
        test_data = load_test_data(str(test_file))
        if not test_data:
            logger.error("没有加载到测试数据")
            return

        # 创建RAG流水线
        pipeline = RAGPipeline(db_dir)

        # 根据参数选择处理方式
        if args.multiprocess:
            # 多进程处理
            logger.info(f"使用多进程处理，进程数: {args.num_processes}")
            logger.info("提示：基于你的模型限制 (RPM: 10,000, TPM: 400,000)，建议使用4-8个进程")

            results = pipeline.process_batch_multiprocess(
                test_data,
                str(output_file),
                resume_from=args.resume_from,
                num_processes=args.num_processes
            )
        else:
            # 单进程处理
            results = pipeline.process_batch(test_data, str(output_file), resume_from=args.resume_from)

        logger.info("=" * 60)
        logger.info(f"处理完成！结果已保存到: {output_file}")
        logger.info(f"总共处理了 {len(results)} 个问题")
        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"主程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Windows多进程支持
    mp.set_start_method('spawn', force=True)
    main()