#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库匹配器测试脚本

基于train.json测试数据库匹配器的准确性，
检查top1匹配结果是否与期望的filename一致
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from dotenv import load_dotenv
import os

# 导入数据库匹配器
from modules.vector_retriever import DatabaseMatcher

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseMatcherTester:
    """数据库匹配器测试器"""
    
    def __init__(self, db_dir: Path, train_file: Path):
        """
        初始化测试器
        
        Args:
            db_dir: 数据库目录
            train_file: 训练数据文件
        """
        self.db_dir = db_dir
        self.train_file = train_file
        self.matcher = None
        self.train_data = []
        
        # 加载训练数据
        self._load_train_data()
        
        # 初始化匹配器
        self._init_matcher()
    
    def _load_train_data(self):
        """加载训练数据"""
        try:
            with open(self.train_file, 'r', encoding='utf-8') as f:
                self.train_data = json.load(f)
            logger.info(f"✅ 成功加载 {len(self.train_data)} 个训练样本")
        except Exception as e:
            logger.error(f"❌ 加载训练数据失败: {e}")
            self.train_data = []
    
    def _init_matcher(self):
        """初始化数据库匹配器"""
        try:
            logger.info("🔄 初始化数据库匹配器...")
            self.matcher = DatabaseMatcher(self.db_dir)
            logger.info("✅ 数据库匹配器初始化完成")
        except Exception as e:
            logger.error(f"❌ 数据库匹配器初始化失败: {e}")
            self.matcher = None
    
    def extract_expected_project_name(self, filename: str) -> str:
        """
        从文件名提取期望的项目名称
        
        Args:
            filename: 文件名，如 "联邦制药-港股公司研究报告-创新突破三靶点战略联姻诺和诺德-25071225页.pdf"
            
        Returns:
            项目名称，如 "联邦制药-港股公司研究报告-创新突破三靶点战略联姻诺和诺德-25071225页"
        """
        # 移除.pdf后缀
        if filename.endswith('.pdf'):
            return filename[:-4]
        return filename
    
    def get_project_name_from_db_id(self, db_name: str) -> str:
        """
        从数据库ID获取项目名称
        
        Args:
            db_name: 数据库名称/ID
            
        Returns:
            项目名称
        """
        if not self.matcher or not self.matcher.db_metadata:
            return db_name
        
        metadata = self.matcher.db_metadata.get(db_name, {})
        return metadata.get('project_name', db_name)
    
    def test_single_question(self, question: str, expected_filename: str) -> Tuple[bool, str, List[str], List[float]]:
        """
        测试单个问题的数据库匹配

        Args:
            question: 问题文本
            expected_filename: 期望的文件名

        Returns:
            (是否在top3中匹配成功, top1项目名, top3项目名列表, top3分数列表)
        """
        if not self.matcher:
            return False, "", [], []

        try:
            # 使用数据库匹配器查找top3匹配
            matched_dbs = self.matcher.match_databases(question, max_matches=3)

            if not matched_dbs:
                return False, "", [], []

            # 获取top1和top3的项目名称和分数
            top1_db_name, top1_score = matched_dbs[0]
            top3_db_names = [db_name for db_name, score in matched_dbs]
            top3_scores = [score for db_name, score in matched_dbs]

            # 从数据库ID获取项目名称
            top1_project = self.get_project_name_from_db_id(top1_db_name)
            top3_projects = [self.get_project_name_from_db_id(db_name) for db_name in top3_db_names]

            # 提取期望的项目名称
            expected_project = self.extract_expected_project_name(expected_filename)

            # 检查top3是否包含期望项目
            top3_match = expected_project in top3_projects

            return top3_match, top1_project, top3_projects, top3_scores

        except Exception as e:
            logger.error(f"测试问题时出错: {e}")
            return False, "", [], []
    
    def test_all_questions(self, max_samples: int = None) -> Dict[str, Any]:
        """
        测试所有问题
        
        Args:
            max_samples: 最大测试样本数，None表示全部测试
            
        Returns:
            测试结果统计
        """
        if not self.train_data:
            logger.error("❌ 没有训练数据可测试")
            return {}
        
        test_data = self.train_data[:max_samples] if max_samples else self.train_data
        total_count = len(test_data)
        
        logger.info(f"🚀 开始测试 {total_count} 个样本的数据库匹配...")
        
        # 统计结果
        results = {
            'total_samples': total_count,
            'top3_correct': 0,
            'failed_samples': 0,
            'mismatches': [],
            'detailed_results': []
        }
        
        for i, sample in enumerate(test_data):
            question = sample.get('question', '')
            expected_filename = sample.get('filename', '')
            expected_project = self.extract_expected_project_name(expected_filename)
            
            logger.info(f"📝 [{i+1}/{total_count}] 测试问题: {question[:50]}...")
            
            # 测试匹配
            top3_match, top1_project, top3_projects, top3_scores = self.test_single_question(question, expected_filename)

            # 统计结果
            if top3_match:
                results['top3_correct'] += 1
                if expected_project == top1_project:
                    logger.info(f"✅ TOP1匹配成功: {top1_project}")
                else:
                    top3_rank = top3_projects.index(expected_project) + 1
                    logger.info(f"✅ TOP3匹配成功: 期望在第{top3_rank}位")
            else:
                logger.error(f"❌ TOP3都未匹配")

                # 记录不匹配样本
                mismatch = {
                    'index': i,
                    'question': question[:100] + "..." if len(question) > 100 else question,
                    'expected_project': expected_project,
                    'top1_project': top1_project,
                    'top3_projects': top3_projects,
                    'top3_scores': top3_scores,
                    'top3_match': top3_match
                }
                results['mismatches'].append(mismatch)
            
            # 记录详细结果
            detailed_result = {
                'index': i,
                'question': question,
                'expected_filename': expected_filename,
                'expected_project': expected_project,
                'top1_project': top1_project,
                'top3_projects': top3_projects,
                'top3_scores': top3_scores,
                'top3_match': top3_match
            }
            results['detailed_results'].append(detailed_result)
            
            # 进度显示
            if (i + 1) % 10 == 0:
                current_top3_rate = (results['top3_correct'] / (i + 1)) * 100
                logger.info(f"📊 当前进度: {i+1}/{total_count} | TOP3: {current_top3_rate:.1f}%")
        
        # 计算最终统计
        results['top3_accuracy'] = (results['top3_correct'] / total_count) * 100
        results['failed_samples'] = total_count - results['top3_correct']
        
        return results
    
    def print_summary(self, results: Dict[str, Any]):
        """打印测试摘要"""
        if not results:
            logger.error("❌ 没有测试结果可显示")
            return
        
        print("\n" + "="*60)
        print("数据库匹配器测试结果摘要")
        print("="*60)
        print(f"总样本数: {results['total_samples']}")
        print(f"TOP1准确率: {results['top1_accuracy']:.1f}% ({results['top1_correct']}/{results['total_samples']})")
        print(f"TOP5准确率: {results['top5_accuracy']:.1f}% ({results['top1_correct'] + results['top5_correct']}/{results['total_samples']})")
        print(f"完全失败数: {results['failed_samples']}")
        
        # 显示前10个不匹配样本
        mismatches = results.get('mismatches', [])
        if mismatches:
            print("\n" + "-"*60)
            print("不匹配样本示例 (前10个):")
            print("-"*60)
            for i, mismatch in enumerate(mismatches[:50]):
                print(f"\n{i+1}. 样本索引: {mismatch['index']}")
                print(f"   问题: {mismatch['question']}")
                print(f"   期望项目: {mismatch['expected_project']}")
                print(f"   TOP1项目: {mismatch['top1_project']}")
                print(f"   TOP5匹配: {'√' if mismatch['top5_match'] else 'X'}")
                if len(mismatch.get('top5_scores', [])) > 0:
                    print(f"   TOP1分数: {mismatch['top5_scores'][0]:.3f}")
                if mismatch['top5_match']:
                    expected = mismatch['expected_project']
                    top5 = mismatch['top5_projects']
                    rank = top5.index(expected) + 1 if expected in top5 else -1
                    print(f"   期望项目在TOP5中排名: 第{rank}位")
                    if len(mismatch.get('top5_scores', [])) > rank - 1:
                        print(f"   期望项目分数: {mismatch['top5_scores'][rank-1]:.3f}")
                print(f"   TOP5详情:")
                expected = mismatch['expected_project']
                for j, (proj, score) in enumerate(zip(mismatch.get('top5_projects', []), mismatch.get('top5_scores', [])), 1):
                    mark = "★" if proj == expected else " "
                    print(f"     {j}. {mark} {proj[:60]}... (分数: {score:.3f})")
        
        print("\n" + "="*60)
    
    def save_results(self, results: Dict[str, Any], output_file: str = "database_matcher_test_results.json"):
        """保存测试结果"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"✅ 测试结果已保存到: {output_file}")
        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    # 配置路径
    current_dir = Path.cwd()
    db_dir = current_dir / "data" / "db"
    train_file = current_dir / "data" / "train.json"
    
    # 检查必要文件
    if not db_dir.exists():
        logger.error(f"❌ 数据库目录不存在: {db_dir}")
        return
    
    if not train_file.exists():
        logger.error(f"❌ 训练数据文件不存在: {train_file}")
        return
    
    print("=" * 60)
    print("数据库匹配器测试脚本")
    print("=" * 60)
    
    # 简单的交互式配置
    try:
        print("\n请选择测试模式:")
        print("1. 快速测试 (20个样本)")
        print("2. 中等测试 (100个样本)")  
        print("3. 大规模测试 (300个样本)")
        print("4. 完整测试 (全部711个样本)")
        
        choice = input("请输入选择 (1-4, 默认1): ").strip()
        if not choice:
            choice = "1"
            
        sample_configs = {
            "1": 20,
            "2": 100,
            "3": 300,
            "4": None
        }
        
        max_samples = sample_configs.get(choice, 20)
        
        print(f"\n将测试 {'全部' if max_samples is None else max_samples} 个样本")
        print("-" * 60)
        
        # 创建测试器
        tester = DatabaseMatcherTester(db_dir, train_file)
        
        # 执行测试
        logger.info("🚀 开始数据库匹配器测试...")
        results = tester.test_all_questions(max_samples=max_samples)
        
        # 保存和显示结果
        tester.save_results(results, "database_matcher_test_results.json")
        tester.print_summary(results)
        
        logger.info("🎉 数据库匹配器测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()